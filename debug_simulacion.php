<?php
// Archivo de debug para la simulación de venta
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', 'debug_errors.log');

require_once 'db_connection.php';
header('Content-Type: application/json');

try {
    echo "Iniciando debug de simulación...\n";
    
    // Verificar conexión a la base de datos
    echo "Probando conexión a la base de datos...\n";
    $conn = getConnection();
    echo "Conexión exitosa!\n";
    
    // Simular los datos que se envían desde el frontend
    $testData = [
        'simulacion' => true,
        'productos' => [
            [
                'repuesto_id' => '1', // Vamos a usar un ID que sabemos que existe
                'nombre' => 'Valvulas Escape 4U',
                'cantidad' => 1
            ]
        ],
        'referencia' => 'Simulación de venta - Debug',
        'usuario_id' => 1,
        'almacen_id' => 1
    ];
    
    echo "Datos de prueba preparados:\n";
    echo json_encode($testData, JSON_PRETTY_PRINT) . "\n";
    
    // Verificar si existe el repuesto_id en la base de datos
    echo "Verificando si existe el repuesto_id...\n";
    $stmt = $conn->prepare("SELECT id, sku, nombre FROM repuesto WHERE id = :id");
    $stmt->bindParam(':id', $testData['productos'][0]['repuesto_id']);
    $stmt->execute();
    $repuesto = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($repuesto) {
        echo "Repuesto encontrado: " . json_encode($repuesto) . "\n";
    } else {
        echo "Repuesto NO encontrado con ID: " . $testData['productos'][0]['repuesto_id'] . "\n";
        
        // Buscar repuestos disponibles
        echo "Buscando repuestos disponibles...\n";
        $stmt = $conn->prepare("SELECT id, sku, nombre FROM repuesto LIMIT 5");
        $stmt->execute();
        $repuestos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "Repuestos disponibles: " . json_encode($repuestos, JSON_PRETTY_PRINT) . "\n";
    }
    
    // Verificar stock disponible
    echo "Verificando stock...\n";
    $stmt = $conn->prepare("SELECT * FROM stock WHERE repuesto_id = :repuesto_id AND almacen_id = :almacen_id");
    $stmt->bindParam(':repuesto_id', $testData['productos'][0]['repuesto_id']);
    $stmt->bindParam(':almacen_id', $testData['almacen_id']);
    $stmt->execute();
    $stock = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Stock encontrado: " . json_encode($stock, JSON_PRETTY_PRINT) . "\n";
    
    // Verificar estructura de la tabla movimiento_inventario
    echo "Verificando estructura de movimiento_inventario...\n";
    $stmt = $conn->prepare("DESCRIBE movimiento_inventario");
    $stmt->execute();
    $estructura = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "Estructura de movimiento_inventario: " . json_encode($estructura, JSON_PRETTY_PRINT) . "\n";
    
    echo "Debug completado exitosamente!\n";
    
} catch(Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
?>
