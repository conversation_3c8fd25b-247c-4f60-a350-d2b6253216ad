<?php
session_start();

// Define los directorios permitidos
$allowedDirs = [
    'Documents/DTE/',         // Para documentos tributarios XML
    'Documents/sobreEnvio/',  // Para sobres de envío
    'Documents/PDF_88/'       // Para PDFs generados
];

// Verificar si el usuario está autorizado
$autorizado = true; // Implementar verificación real de autenticación aquí

if (!$autorizado) {
    header('HTTP/1.0 403 Forbidden');
    echo 'Acceso denegado';
    exit;
}

$file = $_GET['file'] ?? '';
$type = $_GET['type'] ?? 'pdf';

// Validar que el archivo solicitado esté en un directorio permitido
$isAllowed = false;
foreach ($allowedDirs as $dir) {
    if (strpos($file, $dir) === 0) {
        $isAllowed = true;
        break;
    }
}

if (!$isAllowed) {
    header('HTTP/1.0 403 Forbidden');
    echo 'Acceso denegado';
    exit;
}

// Verificar que el archivo existe
if (!file_exists($file)) {
    header('HTTP/1.0 404 Not Found');
    echo 'Archivo no encontrado';
    exit;
}

// Configurar headers según el tipo de archivo
if ($type === 'xml') {
    header('Content-Type: application/xml');
    header('Content-Disposition: attachment; filename="' . basename($file) . '"');
} else {
    header('Content-Type: application/pdf');
    if (isset($_GET['view']) && $_GET['view'] === 'true') {
        header('Content-Disposition: inline; filename="' . basename($file) . '"');
    } else {
        header('Content-Disposition: attachment; filename="' . basename($file) . '"');
    }
}

// Enviar el archivo
readfile($file);
?>
