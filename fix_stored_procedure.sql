-- Script para corregir el procedimiento almacenado obtener_siguiente_folio

-- Primero, verificamos si hay registros en folios_caf para tipo_documento = 61 (Nota de Crédito)
SELECT * FROM folios_caf WHERE tipo_documento = 61;

-- Eliminamos el procedimiento existente
DROP PROCEDURE IF EXISTS obtener_siguiente_folio;

-- Creamos el procedimiento corregido
DELIMITER //
CREATE PROCEDURE obtener_siguiente_folio(
    IN p_tipo_documento INTEGER
)
BEGIN
    DECLARE v_folio INTEGER;
    DECLARE v_ruta VARCHAR(255);
    DECLARE v_rango_final INTEGER;
    DECLARE v_id INTEGER;
    DECLARE v_error BOOLEAN DEFAULT FALSE;
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION SET v_error = TRUE;

    START TRANSACTION;

    -- <PERSON>leccionar y bloquear el registro para actualización
    SELECT id, siguiente_folio, ruta_archivo, rango_final
    INTO v_id, v_folio, v_ruta, v_rango_final
    FROM folios_caf
    WHERE tipo_documento = p_tipo_documento
      AND activo = true
      AND siguiente_folio <= rango_final
    ORDER BY rango_inicial
    LIMIT 1
    FOR UPDATE;

    IF v_folio IS NOT NULL THEN
        -- Calcular el nuevo valor de siguiente_folio
        SET @nuevo_folio = v_folio + 1;

        -- Determinar si este es el último folio del rango
        SET @es_ultimo_folio = IF(@nuevo_folio > v_rango_final, 1, 0);

        -- Actualizar el siguiente folio disponible
        UPDATE folios_caf
        SET siguiente_folio = @nuevo_folio,
            activo = IF(@es_ultimo_folio = 1, 0, 1)
        WHERE id = v_id;

        -- Registrar la actualización para verificación
        INSERT INTO log_folios (tipo_documento, folio_usado, rango_final, es_ultimo, fecha)
        VALUES (p_tipo_documento, v_folio, v_rango_final, @es_ultimo_folio, NOW());

        IF v_error THEN
            ROLLBACK;
            SELECT NULL as folio, NULL as ruta_archivo;
        ELSE
            COMMIT;
            SELECT v_folio as folio, v_ruta as ruta_archivo, @es_ultimo_folio as es_ultimo_folio;
        END IF;
    ELSE
        SELECT NULL as folio, NULL as ruta_archivo, NULL as es_ultimo_folio;
    END IF;
END //
DELIMITER ;

-- Crear tabla de log si no existe
CREATE TABLE IF NOT EXISTS log_folios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tipo_documento INT NOT NULL,
    folio_usado INT NOT NULL,
    rango_final INT NOT NULL,
    es_ultimo TINYINT(1) NOT NULL,
    fecha DATETIME NOT NULL
);

-- Verificar que el procedimiento se ha creado correctamente
SHOW CREATE PROCEDURE obtener_siguiente_folio;

-- Actualizar manualmente el registro de folios_caf para tipo_documento = 61 si es necesario
UPDATE folios_caf SET activo = 0 WHERE tipo_documento = 61 AND siguiente_folio > rango_final;

-- Verificar el estado actual de los folios para tipo_documento = 61
SELECT id, tipo_documento, rango_inicial, rango_final, siguiente_folio, activo
FROM folios_caf
WHERE tipo_documento = 61;
