# Flujo de Datos en el Proceso de Venta - Tata Repuestos

## Flujo Principal de Venta (Carrito de Compras)

1. **Selección de Productos**
   - El usuario añade productos al carrito desde index.php
   - Se mantienen los productos en memoria del cliente (JavaScript)

2. **Proceso de Venta Estándar**
   - Cuando el usuario finaliza la compra desde el carrito:
     - Se envían los datos a process_sale.php
     - Se inicia una transacción en la base de datos
     - INSERCIÓN #1: Se crea un registro en la tabla `venta`
     - INSERCIÓN #2: Se agregan los detalles en la tabla `detalle_orden_compra`
     - Se confirma la transacción (commit)

## Flujo de Generación de DTE (Documento Tributario Electrónico)

1. **Preparación del DTE**
   - El usuario completa el formulario DTE en index.php (canvas lateral)
   - Se recopilan datos del formulario y productos agregados
   - INSERCIÓN #1: Se genera el JSON para enviar a la API

2. **Envío del DTE**
   - Los datos se envían a enviar_dte.php
   - Se consulta la información de folios en la tabla `folios_caf`
   - Se envía la solicitud a la API externa
   - Se recibe y procesa la respuesta XML

3. **Procesamiento del XML**
   - Se llama a procesar_xml.php
   - INSERCIÓN #2: Se guarda el archivo XML en el sistema de archivos
   - INSERCIÓN #3: Se crea un registro en la tabla `tb_facturas_dte`

4. **Generación de PDF**
   - Se genera un PDF a partir del XML mediante una API externa
   - Se guarda el PDF en el sistema de archivos
   - ACTUALIZACIÓN #1: Se actualiza el registro en `tb_facturas_dte` con la ruta del PDF

5. **Guardar Productos del DTE**
   - Se envían los productos a guardar_dte_productos.php
   - INSERCIÓN #4: Se guardan los productos en la tabla `tb_dte_productos`

6. **Actualización de Inventario**
   - Solo para productos con ID de repuesto:
     - Se envían los datos a registrar_salida_inventario.php
     - INSERCIÓN #5: Se registran movimientos en la tabla `movimiento_inventario`
     - Para cada producto se utiliza el método FIFO (First In, First Out)
     - Se verifican los lotes disponibles en la tabla `stock`

7. **Generación de Sobres (opcional)**
   - Para algunos documentos, se requiere enviar a generar_sobre.php
   - INSERCIÓN #6: Se crea un registro en `tb_sobre_envios`
   - ACTUALIZACIÓN #2: Se actualiza `tb_facturas_dte` con el ID del sobre

## Tablas Principales Involucradas

1. `venta` - Registro principal de la venta
2. `detalle_orden_compra` - Detalles de productos vendidos
3. `tb_facturas_dte` - Documentos tributarios electrónicos generados
4. `tb_dte_productos` - Productos incluidos en cada DTE
5. `folios_caf` - Control de folios para documentos tributarios
6. `movimiento_inventario` - Registro de entradas/salidas de inventario
7. `stock` - Control de stock por producto, almacén y lote
8. `tb_sobre_envios` - Registro de sobres enviados al SII
9. `tb_receptores` - Información de clientes/receptores

## Orden de Inserción de Datos (Flujo Completo)

1. Inserción en `venta` (datos básicos de la venta)
2. Inserción en `detalle_orden_compra` (productos vendidos)
3. Inserción en `tb_facturas_dte` (documento tributario)
4. Inserción en `tb_dte_productos` (productos en el DTE)
5. Inserción en `movimiento_inventario` (actualización de inventario)
6. Inserción en `tb_sobre_envios` (envío al SII)
7. Actualización de `tb_facturas_dte` (asociar sobre)

## Notas Adicionales

- El proceso utiliza transacciones para mantener la integridad de los datos
- Existen validaciones en cada paso (stock disponible, folios válidos, etc.)
- Los DTE pueden ser de diferentes tipos (33: Factura, 39: Boleta, 61: Nota de Crédito)
- El sistema solicita folios automáticamente cuando se están agotando