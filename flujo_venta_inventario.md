# Reporte: Flujo de Venta y Actualización de Inventario

## 1. Flujo Completo del Proceso de Venta

El proceso de venta en el sistema Tata Repuestos sigue un flujo estructurado que abarca desde la selección de productos hasta la generación de documentos tributarios y la actualización del inventario.

### 1.1 Selección de Productos y Carrito de Compras

El proceso comienza cuando el usuario selecciona productos desde la interfaz principal:

1. **Búsqueda de productos**: El usuario busca productos por nombre, SKU o código.
2. **Selección de cantidad**: Se establece la cantidad deseada para cada producto.
3. **Adición al carrito**: Los productos se añaden al carrito mediante JavaScript.
4. **Almacenamiento temporal**: Los productos se mantienen en memoria del cliente en el array `cart`.

### 1.2 Proceso de Venta Estándar

Cuando el usuario finaliza la compra desde el carrito:

1. **Recopilación de datos**: Se obtienen los datos del cliente, tipo de documento y condiciones de pago.
2. **Envío al backend**: Los datos se envían a `process_sale.php` mediante una solicitud AJAX.
3. **Procesamiento en base de datos**:
   - Se inicia una transacción en la base de datos.
   - Se crea un registro en la tabla `venta`.
   - Se agregan los detalles en la tabla `detalle_orden_compra`.
   - Se confirma la transacción (commit).
4. **Confirmación al usuario**: Se muestra un mensaje de éxito y se limpia el carrito.

### 1.3 Generación de DTE (Documento Tributario Electrónico)

El proceso de generación de DTE se realiza a través del canvas lateral:

1. **Preparación del DTE**:
   - El usuario completa el formulario DTE con datos del receptor, tipo de documento, etc.
   - Se recopilan los productos a incluir en el documento.
   - Se calculan los totales (neto, IVA, total).

2. **Generación y envío del DTE**:
   - Se genera un JSON con todos los datos necesarios.
   - Los datos se envían a `enviar_dte.php`.
   - Se consulta la información de folios en la tabla `folios_caf`.
   - Se envía la solicitud a la API externa de facturación.
   - Se recibe y procesa la respuesta XML.

3. **Procesamiento del XML**:
   - Se llama a `procesar_xml.php`.
   - Se guarda el archivo XML en el sistema de archivos.
   - Se crea un registro en la tabla `tb_facturas_dte`.

4. **Generación de PDF**:
   - Se genera un PDF a partir del XML mediante una API externa.
   - Se guarda el PDF en el sistema de archivos.
   - Se actualiza el registro en `tb_facturas_dte` con la ruta del PDF.

5. **Guardar Productos del DTE**:
   - Se envían los productos a `guardar_dte_productos.php`.
   - Se guardan los productos en la tabla `tb_dte_productos`.

6. **Actualización de Inventario**:
   - Solo para productos con ID de repuesto:
     - Se envían los datos a `registrar_salida_inventario.php`.
     - Se registran movimientos en la tabla `movimiento_inventario`.
     - Para cada producto se utiliza el método FIFO (First In, First Out).
     - Se verifican los lotes disponibles en la tabla `stock`.

## 2. Actualización del Inventario

### 2.1 Proceso de Actualización

La actualización del inventario es un proceso crítico que ocurre después de generar un DTE o al simular una venta:

1. **Verificación de configuración**:
   - Se verifica si la actualización de inventario está habilitada mediante el checkbox `enableInventarioCheckbox`.
   - Se obtiene el ID del almacén seleccionado.

2. **Preparación de datos**:
   - Se filtran los productos que tienen un ID de repuesto válido.
   - Se prepara la referencia para el movimiento (número de DTE o descripción de simulación).

3. **Envío al backend**:
   - Los datos se envían a `registrar_salida_inventario.php`.
   - Se incluye el ID del DTE, productos, usuario y almacén.

4. **Procesamiento en el backend**:
   - Se inicia una transacción en la base de datos.
   - Para cada producto, se obtienen los lotes disponibles ordenados por fecha (FIFO).
   - Se verifica que haya stock suficiente.
   - Se registran los movimientos de salida en la tabla `movimiento_inventario`.
   - Se actualiza el stock en la tabla `stock`.
   - Se confirma la transacción.

### 2.2 Método FIFO para Consumo de Inventario

El sistema implementa el método FIFO (First In, First Out) para consumir primero el inventario más antiguo:

1. **Obtención de lotes**:
   - Se consultan los lotes disponibles ordenados por fecha de ingreso (más antiguos primero).
   - Se verifica el stock total disponible.

2. **Consumo por lotes**:
   - Se consume la cantidad necesaria del lote más antiguo.
   - Si se agota un lote, se continúa con el siguiente.
   - Se registra cada movimiento con su lote correspondiente.

3. **Actualización de stock**:
   - Se actualiza la cantidad disponible en cada lote.
   - Se mantiene la integridad mediante transacciones.

## 3. Archivos y Endpoints Involucrados

### 3.1 Frontend (JavaScript)

- **`js/app.js`**: Manejo del carrito y proceso de venta estándar.
- **`js/index.js`**: Cálculos de precios y manejo del formulario DTE.
- **`js/dte_productos.js`**: Registro de productos y actualización de inventario.
- **`js/quote-canvas.js`**: Manejo de cotizaciones.
- **`js/simular-venta.js`**: Funcionalidad para simular ventas sin generar DTE.

### 3.2 Backend (PHP)

- **`process_sale.php`**: Procesa la venta estándar.
- **`enviar_dte.php`**: Envía solicitud de DTE a API externa.
- **`procesar_xml.php`**: Procesa la respuesta XML.
- **`guardar_dte_productos.php`**: Guarda productos del DTE.
- **`registrar_salida_inventario.php`**: Actualiza el inventario.

### 3.3 Tablas de Base de Datos

1. **`venta`**: Registro principal de la venta.
2. **`detalle_orden_compra`**: Detalles de productos vendidos.
3. **`tb_facturas_dte`**: Documentos tributarios electrónicos.
4. **`tb_dte_productos`**: Productos incluidos en cada DTE.
5. **`folios_caf`**: Control de folios para documentos tributarios.
6. **`movimiento_inventario`**: Registro de entradas/salidas de inventario.
7. **`stock`**: Control de stock por producto, almacén y lote.
8. **`tb_sobre_envios`**: Registro de sobres enviados al SII.
9. **`tb_receptores`**: Información de clientes/receptores.

## 4. Validación del Proceso de Actualización de Inventario

El proceso de actualización de inventario está correctamente implementado con las siguientes características:

### 4.1 Fortalezas del Sistema

1. **Método FIFO**: Se consumen primero los lotes más antiguos, lo que garantiza una rotación adecuada del inventario.
2. **Transacciones**: Se utilizan transacciones para mantener la integridad de los datos, evitando actualizaciones parciales.
3. **Validación de Stock**: Se verifica que haya stock suficiente antes de procesar la venta.
4. **Opción de Desactivación**: El usuario puede desactivar la actualización de inventario cuando sea necesario.
5. **Selección de Almacén**: Se permite seleccionar el almacén de origen para mayor flexibilidad.
6. **Trazabilidad**: Cada movimiento se registra con una referencia clara al documento que lo originó.

### 4.2 Flujo de Datos en la Actualización de Inventario

```
[Generación DTE] → [Filtrado de productos con ID] → [registrar_salida_inventario.php]
                                                   ↓
[Verificación de stock] ← [Consulta de lotes (FIFO)] ← [Inicio de transacción]
                ↓
[Registro en movimiento_inventario] → [Actualización en tabla stock] → [Commit]
```

## 5. Nueva Funcionalidad: Simulación de Venta

Se ha implementado una nueva funcionalidad que permite simular una venta que solo afecta al inventario, sin generar un DTE:

### 5.1 Características

- **Botón dedicado**: Se agregó un botón "Simular Venta (Solo Inventario)" en el formulario DTE.
- **Proceso independiente**: Funciona de manera independiente al flujo normal de venta.
- **Actualización selectiva**: Solo actualiza el inventario de los productos que tienen ID de repuesto.
- **Confirmación de usuario
</augment_code_snippet>