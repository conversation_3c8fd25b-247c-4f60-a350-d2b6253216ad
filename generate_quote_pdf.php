<?php
session_start();
require_once 'vendor/autoload.php'; // Asumiendo que TCPDF está instalado via Composer

// Si no está instalado via Composer, usar:
// require_once 'tcpdf/tcpdf.php';

// Configurar zona horaria para Santiago de Chile
date_default_timezone_set('America/Santiago');

// Verificar autenticación
if (!isset($_SESSION['usuario_id'])) {
    die('No autorizado');
}

// Recibir datos de la cotización
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['client']) || !isset($input['items'])) {
    die('Datos inválidos');
}

// Clase personalizada para PDF térmico
class ThermalPDF extends TCPDF {
    
    public function Header() {
        // Logo o nombre de la empresa
        $this->SetFont('helvetica', 'B', 12);
        $this->Cell(0, 5, 'TATA REPUESTOS', 0, 1, 'C');
        $this->SetFont('helvetica', '', 8);
        $this->Cell(0, 4, 'RUT: 12.345.678-9', 0, 1, 'C');
        $this->Cell(0, 4, 'Dirección: Manuel montt 1286, Local 2', 0, 1, 'C');
        $this->Cell(0, 4, 'Teléfono: +56989370572', 0, 1, 'C');
        $this->Ln(3);
        
        // Línea separadora
        $this->Cell(0, 0, '', 'T', 1);
        $this->Ln(2);
    }
    
    public function Footer() {
        $this->SetY(-20);
        $this->SetFont('helvetica', 'I', 7);
        $this->Cell(0, 4, 'Gracias por su preferencia', 0, 1, 'C');
        $this->Cell(0, 4, 'Cotización válida por 30 días', 0, 1, 'C');
    }
}

// Crear instancia del PDF
// 80mm = 80 * 0.0393701 = 3.15 pulgadas
$pdf = new ThermalPDF('P', 'mm', array(80, 297), true, 'UTF-8', false);

// Configuración del documento
$pdf->SetCreator('TATA REPUESTOS');
$pdf->SetAuthor('TATA REPUESTOS');
$pdf->SetTitle('Cotización #' . date('Ymd-His'));
$pdf->SetSubject('Cotización de Productos');

// Configurar márgenes para impresora térmica
$pdf->SetMargins(5, 5, 5);
$pdf->SetHeaderMargin(5);
$pdf->SetFooterMargin(5);

// Configurar saltos de página automáticos
$pdf->SetAutoPageBreak(TRUE, 25);

// Configurar factor de escala de imagen
$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

// Agregar página
$pdf->AddPage();

// Título de la cotización
$pdf->SetFont('helvetica', 'B', 10);
$pdf->Cell(0, 5, 'COTIZACIÓN', 0, 1, 'C');
$pdf->SetFont('helvetica', '', 8);
$pdf->Cell(0, 4, 'N° ' . date('Ymd-His'), 0, 1, 'C');
$pdf->Cell(0, 4, 'Fecha: ' . date('d/m/Y H:i'), 0, 1, 'C');
$pdf->Ln(3);

// Información del cliente
$pdf->SetFont('helvetica', 'B', 8);
$pdf->Cell(0, 4, 'DATOS DEL CLIENTE', 0, 1, 'L');
$pdf->SetFont('helvetica', 'B', 8);
$pdf->Cell(0, 4, 'Nombre: ' . $input['client']['name'], 0, 1, 'L');
if (!empty($input['client']['rut'])) {
    $pdf->Cell(0, 4, 'RUT: ' . $input['client']['rut'], 0, 1, 'L');
}
if (!empty($input['client']['email'])) {
    $pdf->Cell(0, 4, 'Email: ' . $input['client']['email'], 0, 1, 'L');
}
if (!empty($input['client']['phone'])) {
    $pdf->Cell(0, 4, 'Teléfono: ' . $input['client']['phone'], 0, 1, 'L');
}
$pdf->Ln(3);

// Línea separadora
$pdf->Cell(0, 0, '', 'T', 1);
$pdf->Ln(2);

// Encabezado de productos
$pdf->SetFont('helvetica', 'B', 8);
$pdf->Cell(0, 4, 'PRODUCTOS', 0, 1, 'L');
$pdf->Ln(1);

// Lista de productos
$pdf->SetFont('helvetica', 'B', 7);
$subtotal = 0;

foreach ($input['items'] as $item) {
    $itemTotal = $item['price'] * $item['quantity'];
    $subtotal += $itemTotal;
    
    // Nombre del producto
    $pdf->MultiCell(0, 4, $item['name'], 0, 'L', 0, 1);
    
    // Cantidad, precio unitario y total
    $pdf->Cell(10, 4, $item['quantity'] . 'x', 0, 0, 'L');
    $pdf->Cell(30, 4, '$' . number_format($item['price'], 0, ',', '.'), 0, 0, 'R');
    $pdf->Cell(30, 4, '$' . number_format($itemTotal, 0, ',', '.'), 0, 1, 'R');
    
    // Ya no mostramos la descripción
    
    $pdf->Ln(1);
}

// Línea separadora
$pdf->Cell(0, 0, '', 'T', 1);
$pdf->Ln(2);

// Totales
$iva = $subtotal * 0.19;
$total = $subtotal + $iva;

$pdf->SetFont('helvetica', 'B', 8);
$pdf->Cell(40, 4, 'Subtotal:', 0, 0, 'R');
$pdf->Cell(30, 4, '$' . number_format($subtotal, 0, ',', '.'), 0, 1, 'R');

$pdf->Cell(40, 4, 'IVA (19%):', 0, 0, 'R');
$pdf->Cell(30, 4, '$' . number_format($iva, 0, ',', '.'), 0, 1, 'R');

$pdf->SetFont('helvetica', 'B', 9);
$pdf->Cell(40, 5, 'TOTAL:', 0, 0, 'R');
$pdf->Cell(30, 5, '$' . number_format($total, 0, ',', '.'), 0, 1, 'R');

// Notas adicionales
if (!empty($input['notes'])) {
    $pdf->Ln(3);
    $pdf->Cell(0, 0, '', 'T', 1);
    $pdf->Ln(2);
    $pdf->SetFont('helvetica', 'B', 7);
    $pdf->Cell(0, 4, 'NOTAS:', 0, 1, 'L');
    $pdf->SetFont('helvetica', 'B', 7);
    $pdf->MultiCell(0, 3, $input['notes'], 0, 'L', 0, 1);
}

// Generar el PDF
$pdfContent = $pdf->Output('', 'S');

// Enviar headers para descarga
header('Content-Type: application/pdf');
header('Content-Disposition: attachment; filename="Cotizacion_' . date('Ymd_His') . '.pdf"');
header('Content-Length: ' . strlen($pdfContent));

echo $pdfContent;
?>