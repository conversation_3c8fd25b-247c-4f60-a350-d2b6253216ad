<?php
require_once 'db_connection.php';
header('Content-Type: application/json');

try {
    error_log("Iniciando get_modelos.php");
    
    if (!isset($_GET['marca_id'])) {
        throw new Exception('marca_id no está definido en la solicitud');
    }

    if (!is_numeric($_GET['marca_id'])) {
        throw new Exception('marca_id no es un número válido: ' . $_GET['marca_id']);
    }

    $marca_id = (int)$_GET['marca_id'];
    error_log("Procesando solicitud para marca_id: " . $marca_id);

    $conn = getConnection();
    
    $sql = "SELECT id, nombre FROM modelo WHERE marca_id = ? AND activo = 1 ORDER BY nombre ASC";
    error_log("Ejecutando consulta SQL: " . $sql);
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$marca_id]);
    
    $resultados = $stmt->fetchAll(PDO::FETCH_ASSOC);
    error_log("Número de modelos encontrados: " . count($resultados));
    error_log("Resultados: " . json_encode($resultados));

    echo json_encode($resultados);

} catch(Exception $e) {
    error_log("Error en get_modelos.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}