<?php
session_start();
header('Content-Type: application/json; charset=utf-8');

date_default_timezone_set('America/Santiago');

if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => 'No autorizado']);
    exit;
}

require_once 'db_connection.php';

$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
if ($id <= 0) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'ID inválido']);
    exit;
}

try {
    $conn = getConnection();

    // Obtener datos principales de la cotización
    $stmt = $conn->prepare("SELECT id, numero, DATE_FORMAT(fecha, '%d-%m-%Y %H:%i') AS fecha, cliente_nombre, cliente_rut, cliente_email, cliente_telefono, subtotal, iva, total, notas FROM tb_cotizaciones WHERE id = :id");
    $stmt->execute(['id' => $id]);
    $quote = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$quote) {
        http_response_code(404);
        echo json_encode(['status' => 'error', 'message' => 'Cotización no encontrada']);
        exit;
    }

    // Obtener productos de la cotización
    $stmtItems = $conn->prepare("SELECT nombre, precio_unitario, cantidad FROM tb_cotizacion_items WHERE cotizacion_id = :id");
    $stmtItems->execute(['id' => $id]);
    $items = $stmtItems->fetchAll(PDO::FETCH_ASSOC);

    $quote['items'] = $items;

    echo json_encode(['status' => 'success', 'quote' => $quote], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => 'Error al obtener detalle', 'error_details' => $e->getMessage()]);
}
?>
