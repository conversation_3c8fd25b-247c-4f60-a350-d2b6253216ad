<?php
session_start();
// Configurar zona horaria para Santiago de Chile
date_default_timezone_set('America/Santiago');

// Verificar autenticación
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    http_response_code(401);
    echo json_encode([
        'status' => 'error',
        'message' => 'No autorizado'
    ]);
    exit;
}

// Función para enviar respuesta JSON
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// Conexión a la base de datos
require_once 'db_connection.php';

try {
    // Recibir datos de la cotización
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['client']) || !isset($input['items']) || empty($input['items'])) {
        sendJsonResponse([
            'status' => 'error',
            'message' => 'Datos de cotización incompletos'
        ], 400);
    }

    // Obtener conexión a la base de datos
    $conn = getConnection();
    
    // Verificar que la conexión sea válida
    if (!($conn instanceof PDO)) {
        sendJsonResponse([
            'status' => 'error',
            'message' => 'Error de conexión a la base de datos'
        ], 500);
    }

    // Iniciar transacción
    $conn->beginTransaction();

    // Generar número único de cotización
    $numeroPrefix = 'COT-';
    $numeroSuffix = date('Ymd-His');
    $numeroCotizacion = $numeroPrefix . $numeroSuffix;

    // Calcular totales
    $subtotal = 0;
    foreach ($input['items'] as $item) {
        $subtotal += $item['price'] * $item['quantity'];
    }
    $iva = $subtotal * 0.19;
    $total = $subtotal + $iva;

    // Insertar en la tabla tb_cotizaciones
    $stmt = $conn->prepare("
        INSERT INTO tb_cotizaciones (
            numero, 
            fecha, 
            cliente_nombre, 
            cliente_rut, 
            cliente_email, 
            cliente_telefono, 
            subtotal, 
            iva, 
            total, 
            notas, 
            usuario_id, 
            estado
        ) VALUES (
            :numero, 
            NOW(), 
            :cliente_nombre, 
            :cliente_rut, 
            :cliente_email, 
            :cliente_telefono, 
            :subtotal, 
            :iva, 
            :total, 
            :notas, 
            :usuario_id, 
            'pendiente'
        )
    ");

    $stmt->execute([
        'numero' => $numeroCotizacion,
        'cliente_nombre' => $input['client']['name'],
        'cliente_rut' => $input['client']['rut'] ?? null,
        'cliente_email' => $input['client']['email'] ?? null,
        'cliente_telefono' => $input['client']['phone'] ?? null,
        'subtotal' => $subtotal,
        'iva' => $iva,
        'total' => $total,
        'notas' => $input['notes'] ?? null,
        'usuario_id' => $_SESSION['usuario_id'] ?? null
    ]);

    $cotizacionId = $conn->lastInsertId();

    // Insertar los items de la cotización
    $stmtItems = $conn->prepare("
        INSERT INTO tb_cotizacion_items (
            cotizacion_id, 
            repuesto_id, 
            nombre, 
            precio_unitario, 
            cantidad, 
            total_item
        ) VALUES (
            :cotizacion_id, 
            :repuesto_id, 
            :nombre, 
            :precio_unitario, 
            :cantidad, 
            :total_item
        )
    ");

    foreach ($input['items'] as $item) {
        $totalItem = $item['price'] * $item['quantity'];
        $repuestoId = !empty($item['id']) && strpos($item['id'], 'manual_') === false ? $item['id'] : null;
        
        $stmtItems->execute([
            'cotizacion_id' => $cotizacionId,
            'repuesto_id' => $repuestoId,
            'nombre' => $item['name'],
            'precio_unitario' => $item['price'],
            'cantidad' => $item['quantity'],
            'total_item' => $totalItem
        ]);
    }

    // Confirmar la transacción
    $conn->commit();

    // Enviar respuesta exitosa
    sendJsonResponse([
        'status' => 'success',
        'message' => 'Cotización guardada correctamente',
        'cotizacion_id' => $cotizacionId,
        'numero_cotizacion' => $numeroCotizacion
    ]);

} catch (PDOException $e) {
    // Revertir la transacción en caso de error
    if (isset($conn) && $conn instanceof PDO) {
        $conn->rollBack();
    }
    
    sendJsonResponse([
        'status' => 'error',
        'message' => 'Error al guardar la cotización en la base de datos',
        'error_details' => $e->getMessage()
    ], 500);
    
} catch (Exception $e) {
    // Error general
    sendJsonResponse([
        'status' => 'error',
        'message' => 'Error al procesar la solicitud',
        'error_details' => $e->getMessage()
    ], 500);
}
?>