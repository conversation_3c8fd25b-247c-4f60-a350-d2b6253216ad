<?php
// Activar logging de errores
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Crear archivo de log específico para este script
$logFile = __DIR__ . '/inventory_log.txt';

function logMessage($message, $type = 'INFO') {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp][$type] $message\n";
    error_log($logEntry, 3, $logFile);
}

// Log de inicio de solicitud
logMessage("Nueva solicitud recibida");
logMessage("Método HTTP: " . $_SERVER['REQUEST_METHOD']);
logMessage("Headers recibidos: " . json_encode(getallheaders()));

header('Content-Type: application/json');

try {
    // Log del body recibido
    $rawData = file_get_contents('php://input');
    logMessage("Datos recibidos: " . $rawData);
    
    $datos = json_decode($rawData, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Error al decodificar JSON: ' . json_last_error_msg());
    }

    logMessage("Datos decodificados: " . json_encode($datos));

    if (!is_array($datos)) {
        throw new Exception('Los datos recibidos no son un array válido');
    }

    require_once 'db_connection.php';
    $conn = getConnection();
    logMessage("Conexión a base de datos establecida");

    $conn->beginTransaction();
    logMessage("Iniciando transacción");

    $query = "INSERT INTO movimiento_inventario 
              (repuesto_id, almacen_id, tipo_movimiento, cantidad, 
               referencia_documento, usuario, notas) 
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad, 
                      :referencia_documento, :usuario, :notas)";
    
    $stmt = $conn->prepare($query);
    logMessage("Query preparada: " . $query);

    foreach ($datos as $movimiento) {
        logMessage("Procesando movimiento: " . json_encode($movimiento));
        
        try {
            $result = $stmt->execute([
                ':repuesto_id' => (int)$movimiento['repuesto_id'],
                ':almacen_id' => (int)$movimiento['almacen_id'],
                ':tipo_movimiento' => $movimiento['tipo_movimiento'],
                ':cantidad' => (int)$movimiento['cantidad'],
                ':referencia_documento' => $movimiento['referencia_documento'],
                ':usuario' => $movimiento['usuario'],
                ':notas' => $movimiento['notas']
            ]);
            
            if ($result) {
                logMessage("Movimiento insertado correctamente");
            } else {
                logMessage("Error al insertar movimiento", "ERROR");
                throw new Exception('Error al insertar: ' . implode(', ', $stmt->errorInfo()));
            }
        } catch (PDOException $e) {
            logMessage("Error PDO: " . $e->getMessage(), "ERROR");
            throw new Exception('Error al insertar: ' . $e->getMessage());
        }
    }

    $conn->commit();
    logMessage("Transacción completada");
    
    echo json_encode([
        'status' => 'success',
        'message' => 'Movimientos registrados correctamente'
    ]);

} catch (Exception $e) {
    logMessage("Error: " . $e->getMessage(), "ERROR");
    logMessage("Stack trace: " . $e->getTraceAsString(), "ERROR");
    
    if (isset($conn)) {
        $conn->rollBack();
        logMessage("Transacción revertida", "WARNING");
    }
    
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        $conn = null;
        logMessage("Conexión cerrada");
    }
}
?>
