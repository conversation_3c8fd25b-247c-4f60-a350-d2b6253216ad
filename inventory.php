<?php
// Incluir verificación de autenticación
require_once 'auth_check.php';

require_once 'db_connection.php';

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Configuración adicional de caché
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
header("Expires: Sat, 26 Jul 1997 05:00:00 GMT"); // Fecha en el pasado
?>
<!DOCTYPE html>
<html lang="es">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventario - TATA REPUESTOS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles/table.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="styles/header.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="styles/inventory.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="styles/autocomplete.css?v=<?php echo time(); ?>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.css">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    
    <style>
        /* Estilos para los botones de iconos */
        .icon-button {
            background: none;
            border: none;
            padding: 0;
            color: inherit;
            line-height: 1;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
            cursor: pointer;
        }

        .icon-button i {
            margin: 0;
        }

        /* Estilos para los contadores */
        .dte-count, .quote-count {
            position: absolute;
            top: -10px;
            right: -10px;
            background-color: #e74c3c;
            color: white;
            border-radius: 50%;
            padding: 3px 7px;
            font-size: 0.8rem;
            font-weight: bold;
            min-width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
            z-index: 1;
            border: 1px solid white;
            pointer-events: none;
        }
        
        .quote-count {
            background-color: #3498db;
        }

        /* Estilos para el menú desplegable de usuario */
        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 10px;
            min-width: 200px;
            z-index: 1000;
            display: none;
        }

        .icon-container > i.fa-user {
            cursor: pointer;
            position: relative;
        }

        .icon-container > i.fa-user:hover + .user-dropdown,
        .user-dropdown:hover {
            display: block;
        }

        .user-info {
            padding-bottom: 10px;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .user-name {
            display: block;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .user-role {
            display: block;
            font-size: 0.8rem;
            color: #7f8c8d;
            text-transform: capitalize;
        }

        .logout-btn {
            display: flex;
            align-items: center;
            color: #e74c3c;
            text-decoration: none;
            padding: 5px 0;
            font-size: 0.9rem;
        }

        .logout-btn i {
            margin-right: 5px;
        }
    </style>
</head>

<body>

    <!-- Header principal con logo y módulos -->
    <header>
        <nav>
            <div class="logo">
                <i class="fas fa-cogs"></i>
                Tata repuestos
            </div>
            <ul class="modules-menu">
                <li><a href="index.php" class="module-link"><i class="fas fa-cash-register"></i><span>Módulo Venta</span></a></li>
                <li><a href="inventory.php" class="module-link"><i class="fas fa-boxes"></i><span>Módulo Inventario</span></a></li>
                <li><a href="ventas.php" class="module-link"><i class="fas fa-chart-bar"></i><span>Módulo Reportería</span></a></li>
                <li><a href="sobres_envio.php" class="module-link"><i class="fas fa-envelope"></i><span>Módulo de Sobres</span></a></li>
                <li><a href="mod_config.php" class="module-link"><i class="fas fa-cog"></i><span>Módulo Configuración</span></a></li>
            </ul>
            <div class="cart-icon">
                <div class="icon-container">
                    <!-- Icono de Notificación -->
                    <i class="fas fa-bell"></i>

                    <!-- BOTÓN COTIZACIÓN ESTILIZADO COMO ICONO -->
                    <button id="openQuoteBtn" class="icon-button" title="Abrir Cotización">
                         <i class="fas fa-file-invoice-dollar"></i>
                         <span class="quote-count">0</span>
                    </button>

                    <!-- BOTÓN DTE ESTILIZADO COMO ICONO (MANTENIENDO EL ID) -->
                    <button id="openDTEBtn" class="icon-button" title="Abrir DTE">
                         <i class="fas fa-file-invoice"></i>
                         <span class="dte-count">0</span>
                    </button>
                    
                    <!-- Icono de Usuario -->
                    <i class="fas fa-user"></i>
                    <div class="user-dropdown">
                        <div class="user-info">
                            <span class="user-name"><?php echo htmlspecialchars($_SESSION['nombre'] ?? 'Usuario'); ?></span>
                            <span class="user-role"><?php echo htmlspecialchars($_SESSION['rol'] ?? 'Sin rol'); ?></span>
                        </div>
                        <a href="logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Cerrar sesión</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Sección de botones -->
    <div class="sub-header action-buttons">
        <div class="button-container">
            <button class="action-button" onclick="window.openNewProductModal()">
                <i class="fas fa-plus"></i>
                Nuevo repuesto
            </button>
            <button class="action-button" onclick="window.openNewModelModal()">
                <i class="fas fa-plus"></i>
                Nuevo modelo
            </button>
            <button class="action-button" onclick="window.openEntradaModal()" style="background-color: green;">
                <i class="fas fa-plus-circle"></i>
                Ingresar entrada
            </button>
            <button class="action-button" onclick="exportToExcel()">
                <i class="fas fa-file-export"></i>
                Exportar
            </button>
        </div>
    </div>


    <!-- Buscador para la tabla -->
    <!-- Añade esto antes de tu tabla -->


    <!-- Contenedor principal del inventario -->
    <div class="inventory-container">
        <div class="products-table inventory-table">
            <table id="tabla_repuestos" class="table table-striped table-hover sortable-table filterable-table">
                <?php
                  // Generar los encabezados de la tabla
                  echo '<thead><tr>';

                  $headers = [
                      'Imagen',
                      'SKU',
                      'Categoría',
                      'Subcategoría',
                      'Nombre',
                      'Descripción',
                      'Fabricante',
                      'Precio',
                      'Stock',
                      'Acciones'
                  ];

                  foreach ($headers as $index => $header) {
                      // No agregar ordenamiento a la columna de acciones
                      if ($header === 'Acciones') {
                          echo '<th style="font-size: 12px;">' . htmlspecialchars($header) . '</th>';
                      } else {
                          echo '<th style="font-size: 12px;" class="sortable" data-column="' . $index . '" data-sort="' . ($header === 'Precio' || $header === 'Stock' ? 'number' : 'string') . '">
                                  ' . htmlspecialchars($header) . '
                                  <i class="fas fa-sort sort-icon"></i>
                                </th>';
                      }
                  }
                  echo '</tr>';

                  // Generar los campos de búsqueda
                  echo '<tr class="filter-row">';
                  foreach ($headers as $index => $header) {
                    if ($header === 'Acciones') {
                      echo '<th></th>'; // Celda vacía para la columna de acciones
                    } else {
                      echo '<th>
                      <input type="text"
                             class="column-search"
                             data-column="' . $index . '"
                             placeholder="Filtrar..."
                             style="width: 90%; padding: 6px 8px; border-radius: 4px; border: 1px solid #ddd; margin: 4px 0;">
                      </th>';
                    }
                  }
                  echo '</tr></thead>';
                  echo '<tbody>';
                  ?>

                <tbody>
                    <?php
                  try {
                      $conn = getConnection();
                      $stmt = $conn->query("SELECT
                                                r.id,
                                                r.sku,
                                                r.nombre,
                                                r.descripcion,
                                                r.categoria_id,
                                                r.id_subcategoria,
                                                r.precio_compra,
                                                r.precio_venta,
                                                r.stock_minimo,
                                                r.stock_maximo,
                                                r.unidad_medida,
                                                r.ubicacion_almacen,
                                                r.es_original,
                                                r.fabricante,
                                                r.pais_origen,
                                                r.codigo_fabricante,
                                                r.activo,
                                                r.url_imagen,
                                                -- Información de stock
                                                COALESCE(s.cantidad, 0) as stock_actual,
                                                a.nombre as almacen_nombre,
                                                -- Información de categoría y subcategoría
                                                c.nombre as categoria_nombre,
                                                c.color_class,
                                                sc.nombre as subcategoria_nombre,
                                                -- Calcular si está bajo stock mínimo
                                                CASE
                                                    WHEN COALESCE(s.cantidad, 0) <= r.stock_minimo THEN 'Bajo Stock'
                                                    WHEN COALESCE(s.cantidad, 0) <= r.stock_minimo * 1.2 THEN 'Stock Crítico'
                                                    ELSE 'Stock Normal'
                                                END as estado_stock
                                            FROM repuesto r
                                            LEFT JOIN stock s ON r.id = s.repuesto_id
                                            LEFT JOIN almacen a ON s.almacen_id = a.id
                                            LEFT JOIN categoria_repuesto c ON r.categoria_id = c.id
                                            LEFT JOIN categoria_repuesto sc ON r.id_subcategoria = sc.id
                                          WHERE r.activo = 1");
                      $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

                      foreach($products as $product) {
                          echo '<tr data-id="' . htmlspecialchars($product['id']) . '">
                              <td class="text-center">' .
                                  (empty($product['url_imagen']) ?
                                  '<div class="table-image-container"><img src="images/no-image.png" alt="Sin imagen" class="table-image"></div>' :
                                  '<div class="table-image-container" title="Haga clic para ampliar">
                                      <img src="' . htmlspecialchars($product['url_imagen']) . '" alt="' . htmlspecialchars($product['nombre']) . '" class="table-image">
                                      <div class="image-zoom-icon">
                                          <i class="fas fa-search-plus"></i>
                                      </div>
                                  </div>') .
                              '</td>
                              <td>' . htmlspecialchars($product['sku']) . '</td>
                              <td>
                                  <span class="category-tag ' . htmlspecialchars($product['color_class'] ?? 'category-default') . '">
                                      ' . htmlspecialchars($product['categoria_nombre'] ?? 'Sin categoría') . '
                                  </span>
                              </td>
                              <td>' . htmlspecialchars($product['subcategoria_nombre'] ?? 'N/A') . '</td>
                              <td>' . htmlspecialchars($product['nombre']) . '</td>
                              <td class="description-cell" data-bs-toggle="tooltip" data-bs-placement="top" title="' . htmlspecialchars($product['descripcion'] ?? '') . '">' . (empty($product['descripcion']) ? 'N/A' : htmlspecialchars(substr($product['descripcion'], 0, 60)) . (strlen($product['descripcion']) > 60 ? '...' : '')) . '</td>
                              <td>' . htmlspecialchars($product['fabricante'] ?? 'N/A') . '</td>
                              <td>' . (empty($product['precio_venta']) ? 'N/A' : '$' . number_format($product['precio_venta'], 0)) . '</td>
                              <td>' . htmlspecialchars($product['stock_actual'] ?? 'N/A') . '</td>
                              <td class="action-buttons">
                                  <div class="action-container">
                                      <button class="icon-btn edit-btn" title="Editar" onclick="openEditModal(' . htmlspecialchars($product['id']) . ')">
                                          <i class="fas fa-edit"></i>
                                      </button>
                                      <button class="icon-btn assign-btn" title="Asignar" onclick="openAssignModal(' . htmlspecialchars($product['id']) . ')">
                                          <i class="fas fa-plus"></i>
                                      </button>
                                      <button class="icon-btn delete-btn" title="Eliminar" onclick="confirmDeleteProduct(' . htmlspecialchars($product['id']) . ', \'' . htmlspecialchars(addslashes($product['nombre'])) . '\')">
                                          <i class="fas fa-trash"></i>
                                      </button>
                                  </div>
                              </td>
                          </tr>';
                      }
                  } catch(Exception $e) {
                      echo '<tr><td colspan="10">Error al cargar los productos: ' . $e->getMessage() . '</td></tr>';
                  }
                  ?>
                </tbody>
            </table>
        </div>
    </div>



    <!-- Modal de nuevo producto -->
    <div class="modal fade" id="newProductModal" tabindex="-1" aria-labelledby="newProductModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="newProductModalLabel">
                        <i class="fas fa-plus-circle me-2"></i>Nuevo repuesto
                    </h5>
                    <!-- <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button> -->
                </div>
                <div class="modal-body">
                    <form id="newProductForm" enctype="multipart/form-data">


                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="new_categoria_principal" class="form-label">Categoría Principal:</label>
                                <select id="new_categoria_principal" name="new_categoria_principal" class="form-select" required onchange="console.log('Evento onchange nativo disparado'); handleCategoriaChange.call(this);">
                                    <option value="" disabled selected>Seleccione una categoría</option>
                                    <?php
                                    try {
                                        $stmt = $conn->query("SELECT id, nombre FROM categoria_repuesto WHERE categoria_padre_id IS NULL ORDER BY nombre");
                                        while($categoria = $stmt->fetch(PDO::FETCH_ASSOC)) {
                                            echo '<option value="' . htmlspecialchars($categoria['id']) . '">' .
                                                 htmlspecialchars($categoria['nombre']) . '</option>';
                                        }
                                    } catch(Exception $e) {
                                        echo '<option value="">Error al cargar categorías</option>';
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="new_subcategoria" class="form-label">Subcategoría:</label>
                                <select id="new_subcategoria" name="new_subcategoria" class="form-select" required disabled>
                                    <option value="" disabled selected>Seleccione una subcategoría</option>
                                </select>
                            </div>
                        </div>

                        <!-- Script de depuración para el filtrado de categorías -->
                        <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            console.log('Script de depuración cargado');

                            // Verificar si la función handleCategoriaChange existe
                            console.log('handleCategoriaChange existe:', typeof handleCategoriaChange !== 'undefined');

                            // Agregar manejador directo al select de categoría
                            const categoriaSelect = document.getElementById('new_categoria_principal');
                            if (categoriaSelect) {
                                console.log('Select de categoría encontrado');
                                categoriaSelect.addEventListener('change', function() {
                                    console.log('Evento change disparado manualmente');
                                    const categoriaId = this.value;
                                    console.log('Categoría seleccionada:', categoriaId);

                                    const subcategoriaSelect = document.getElementById('new_subcategoria');
                                    if (subcategoriaSelect) {
                                        subcategoriaSelect.disabled = true;
                                        subcategoriaSelect.innerHTML = '<option value="">Cargando...</option>';

                                        // Realizar petición AJAX directamente
                                        const xhr = new XMLHttpRequest();
                                        xhr.open('GET', 'get_subcategorias.php?categoria_id=' + categoriaId, true);
                                        xhr.setRequestHeader('Content-Type', 'application/json');
                                        xhr.setRequestHeader('Cache-Control', 'no-cache');

                                        xhr.onload = function() {
                                            console.log('Respuesta recibida:', xhr.status, xhr.responseText);

                                            if (xhr.status === 200) {
                                                try {
                                                    const data = JSON.parse(xhr.responseText);
                                                    console.log('Datos parseados:', data);

                                                    subcategoriaSelect.innerHTML = '<option value="" disabled selected>Seleccione una subcategoría</option>';

                                                    if (Array.isArray(data)) {
                                                        if (data.length === 0) {
                                                            subcategoriaSelect.innerHTML += '<option value="" disabled>No hay subcategorías disponibles</option>';
                                                        } else {
                                                            data.forEach(function(subcategoria) {
                                                                const option = document.createElement('option');
                                                                option.value = subcategoria.id;
                                                                option.textContent = subcategoria.nombre;
                                                                subcategoriaSelect.appendChild(option);
                                                            });
                                                        }
                                                    } else {
                                                        subcategoriaSelect.innerHTML += '<option value="" disabled>Formato de datos incorrecto</option>';
                                                    }

                                                    subcategoriaSelect.disabled = false;
                                                } catch (e) {
                                                    console.error('Error al parsear JSON:', e);
                                                    subcategoriaSelect.innerHTML = '<option value="">Error al cargar subcategorías</option>';
                                                    subcategoriaSelect.disabled = false;
                                                }
                                            } else {
                                                console.error('Error en la petición:', xhr.status);
                                                subcategoriaSelect.innerHTML = '<option value="">Error al cargar subcategorías</option>';
                                                subcategoriaSelect.disabled = false;
                                            }
                                        };

                                        xhr.onerror = function() {
                                            console.error('Error de red');
                                            subcategoriaSelect.innerHTML = '<option value="">Error de red</option>';
                                            subcategoriaSelect.disabled = false;
                                        };

                                        xhr.send();
                                    } else {
                                        console.error('Select de subcategoría no encontrado');
                                    }
                                });
                            } else {
                                console.error('Select de categoría no encontrado');
                            }
                        });
                        </script>

                        <div class="row">


                            <div class="col-md-6 mb-3">
                                <label for="new_sku" class="form-label">SKU:</label>
                                <input type="text" class="form-control" id="new_sku" name="sku" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="new_nombre" class="form-label">Nombre:</label>
                                <input type="text" class="form-control" id="new_nombre" name="nombre" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="new_descripcion" class="form-label">Descripción:</label>
                            <textarea class="form-control" id="new_descripcion" name="descripcion" rows="3"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="new_precio_compra" class="form-label">Precio Compra:</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="new_precio_compra" name="precio_compra" step="0.01" required>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="new_precio_venta" class="form-label">Precio Venta:</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="new_precio_venta" name="precio_venta" step="0.01" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="new_stock_minimo" class="form-label">Stock Mínimo:</label>
                                <input type="number" class="form-control" id="new_stock_minimo" name="stock_minimo"
                                    required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="new_stock_maximo" class="form-label">Stock Máximo:</label>
                                <input type="number" class="form-control" id="new_stock_maximo" name="stock_maximo"
                                    required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="new_unidad_medida" class="form-label">Unidad de Medida:</label>
                                <select class="form-select" id="new_unidad_medida" name="unidad_medida">
                                    <option value="" disabled selected>Seleccione una unidad</option>
                                    <option value="litros">Litros</option>
                                    <option value="kilogramos">Kilogramos</option>
                                    <option value="unidades">Unidades</option>
                                    <option value="metros">Metros</option>
                                    <option value="milímetros">Milímetros</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="new_ubicacion" class="form-label">Ubicación Almacén:</label>
                                <input type="text" class="form-control" id="new_ubicacion" name="ubicacion_almacen"
                                    value="Temuco" readonly>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="new_es_original" class="form-label">Es Original:</label>
                                <select class="form-select" id="new_es_original" name="es_original">
                                    <option value="1">Sí</option>
                                    <option value="0">No</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="new_fabricante" class="form-label">Fabricante:</label>
                                <input type="text" class="form-control" id="new_fabricante" name="fabricante">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="new_pais_origen" class="form-label">País de Origen:</label>
                                <input type="text" class="form-control" id="new_pais_origen" name="pais_origen">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="new_codigo_fabricante" class="form-label">Código Fabricante:</label>
                                <input type="text" class="form-control" id="new_codigo_fabricante"
                                    name="codigo_fabricante">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="new_imagen" class="form-label">Imagen del Repuesto:</label>
                            <input type="file" class="form-control" id="new_imagen" name="imagen" accept="image/*">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" onclick="handleSaveProduct()">
                        <i class="fas fa-save me-1"></i>Guardar
                    </button>
                </div>
            </div>
        </div>
    </div>


    <!-- Modal de nuevo modelo -->
    <div class="modal fade" id="newModelModal" tabindex="-1" aria-labelledby="newModelModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="newModelModalLabel">
                        <i class="fas fa-car me-2"></i>Nuevo Modelo
                    </h5>
                    <!-- <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button> -->
                </div>
                <div class="modal-body">
                    <form id="newModelForm">
                        <div class="form-group mb-3">
                            <label for="marca_id">Marca:</label>
                            <select class="form-select" id="modelo_new_marca_id" name="modelo_new_marca_id" required>
                                <option value="">Seleccione una marca</option>
                                <?php
                            try {
                                $stmt = $conn->query("SELECT id, nombre FROM marca ORDER BY nombre ASC");
                                while($marca = $stmt->fetch(PDO::FETCH_ASSOC)) {
                                    echo '<option value="' . htmlspecialchars($marca['id']) . '">' .
                                         htmlspecialchars($marca['nombre']) . '</option>';
                                }
                            } catch(Exception $e) {
                                echo '<option value="">Error al cargar marcas</option>';
                            }
                            ?>
                            </select>
                        </div>

                        <div class="form-group mb-3">
                            <label for="modelo_id">Modelo:</label>
                            <select class="form-select" id="modelo_id" name="modelo_id" required>
                                <option value="">Seleccione un modelo</option>
                            </select>
                        </div>

                        <div class="form-group mb-3">
                            <label for="anio_inicio">Año Inicio:</label>
                            <select class="form-select" id="anio_inicio" name="anio_inicio" required>
                                <option value="">Seleccione año inicio</option>
                                <?php
                                for($i = 2024; $i >= 1950; $i--) {
                                    echo "<option value='$i'>$i</option>";
                                }
                                ?>
                            </select>
                        </div>

                        <div class="form-group mb-3">
                            <label for="anio_fin">Año Fin:</label>
                            <select class="form-select" id="anio_fin" name="anio_fin" required>
                                <option value="">Seleccione año fin</option>
                                <?php
                                for($i = 2024; $i >= 1950; $i--) {
                                    echo "<option value='$i'>$i</option>";
                                }
                                ?>
                            </select>
                        </div>

                        <div class="form-group mb-3">
                            <label for="cilindrada">Cilindrada:</label>
                            <select class="form-select" id="cilindrada" name="cilindrada" required>
                                <option value="">Seleccione cilindrada</option>
                                <?php
                                $cilindradas = [1000, 1200, 1400, 1600, 1800, 2000, 2200, 2400, 2600, 2800, 3000];
                                foreach($cilindradas as $cc) {
                                    echo "<option value='$cc'>$cc</option>";
                                }
                                ?>
                            </select>
                        </div>

                        <div class="form-group mb-3">
                            <label for="version">Versión:</label>
                            <input type="text" class="form-control" id="version" name="version"
                                placeholder="Ingrese versión" required>
                        </div>

                        <div class="form-group mb-3">
                            <label for="tipo_motor">Tipo Motor:</label>
                            <select class="form-select" id="tipo_motor" name="tipo_motor" required>
                                <option value="">Seleccione tipo motor</option>
                                <?php
                                $tipos_motor = ['DOHC', 'SOHC', 'OHV', 'Turbo', 'Atmosférico'];
                                foreach($tipos_motor as $tipo) {
                                    echo "<option value='$tipo'>$tipo</option>";
                                }
                                ?>
                            </select>
                        </div>

                        <div class="form-group mb-3">
                            <label for="combustible">Combustible:</label>
                            <select class="form-select" id="combustible" name="combustible" required>
                                <option value="">Seleccione combustible</option>
                                <?php
                                  $combustibles = ['Gasolina', 'Diesel', 'GLP', 'GNC', 'Híbrido', 'Eléctrico'];
                                  foreach($combustibles as $combustible) {
                                      echo "<option value='$combustible'>$combustible</option>";
                                  }
                                  ?>
                            </select>
                        </div>



                        <!-- Rest of your form fields with Bootstrap classes -->
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>Cerrar
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Guardar
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>



    <!-- Modal de entrada de inventario -->
<div class="modal fade" id="entradaModal" tabindex="-1">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-boxes me-2"></i>Ingresar Entrada
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- Primera columna - Datos comunes -->
                    <div class="col-md-4">
                        <h6 class="mb-3">Datos Generales</h6>
                        <div class="mb-3">
                        <label for="almacen_id" class="form-label">Almacén ID</label>
                        <input type="text" class="form-control" id="almacen_id" value=1 placeholder="TEMUCO" readonly>

                        </div>
                        <div class="mb-3">
                            <label for="tipo_movimiento" class="form-label">Tipo Movimiento</label>
                            <select class="form-select" id="tipo_movimiento">
                                <option value="entrada">Entrada</option>
                                <option value="salida">Salida</option>
                                <option value="ajuste">Ajuste</option>
                                <option value="transferencia">Transferencia</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="referencia_documento" class="form-label">Referencia Documento</label>
                            <input type="text" class="form-control" id="referencia_documento">
                        </div>
                        <div class="mb-3">
                            <label for="notas" class="form-label">Notas</label>
                            <textarea class="form-control" id="notas" rows="3"></textarea>
                        </div>
                    </div>

                    <!-- Segunda columna - Agregar repuestos -->
                    <div class="col-md-4">
                        <h6 class="mb-3">Agregar Repuestos</h6>
                        <div class="mb-3">
                            <label for="repuesto_id" class="form-label">SKU <small class="text-muted">(Escriba para buscar)</small></label>
                            <div class="input-group">
                                <input type="text" class="form-control autocomplete-input" id="repuesto_id" placeholder="Escriba para buscar...">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="cantidad" class="form-label">Cantidad</label>
                            <input type="number" class="form-control" id="cantidad">
                        </div>

                        <button type="button" class="btn btn-primary w-100 mb-3" onclick="agregarRepuesto()">
                            <i class="fas fa-plus-circle me-1"></i>Agregar Repuesto
                        </button>
                    </div>

                    <!-- Tercera columna - Repuestos agregados -->
                    <div class="col-md-4">
                        <h6 class="mb-3">Repuestos Agregados</h6>
                        <div class="repuestos-list">
                            <div id="listaRepuestos" class="list-group"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancelar
                </button>
                <button type="button" class="btn btn-primary" onclick="guardarEntrada()">
                    <i class="fas fa-save me-1"></i>Guardar Todo
                </button>
            </div>
        </div>
    </div>
</div>

<!-- CSS -->
<style>
.repuestos-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 8px;
    margin-top: 10px;
    background-color: #f8f9fa;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

.list-group-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 10px 12px;
    margin-bottom: 8px;
    border-radius: 4px;
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    transition: all 0.2s ease;
}

.list-group-item:hover {
    background-color: #f0f7ff;
    border-color: #c2d6f5;
}

.repuesto-info {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.repuesto-nombre {
    font-size: 0.85em;
    color: #6c757d;
    margin-top: 4px;
}

/* Estilo para el modal de entrada */
#entradaModal .modal-content {
    background: linear-gradient(to bottom, #f8f9fa, #ffffff);
    border: none;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

#entradaModal .modal-header {
    background: linear-gradient(to right, #34495e, #4a6285);
    color: white;
    border-radius: 8px 8px 0 0;
}

#entradaModal h6 {
    color: #34495e;
    font-weight: 600;
    border-bottom: 2px solid #eaeaea;
    padding-bottom: 8px;
    margin-bottom: 15px;
}
</style>




<!-- Modal de edición -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalLabel">
                    <i class="fas fa-edit me-2"></i>Editar Producto
                </h5>
                <!-- <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button> -->
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <input type="hidden" id="productId" name="productId">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="categoria_id" class="form-label">Categoría:</label>
                            <select class="form-select" id="categoria_id" name="categoria_id" onchange="loadSubcategorias(this.value)">
                                <option value="">Sin categoría</option>
                                <?php
                                try {
                                    $stmt = $conn->query("SELECT id, nombre FROM categoria_repuesto WHERE categoria_padre_id IS NULL ORDER BY nombre");
                                    while($cat = $stmt->fetch()) {
                                        echo "<option value='" . htmlspecialchars($cat['id']) . "'>" .
                                             htmlspecialchars($cat['nombre']) . "</option>";
                                    }
                                } catch(Exception $e) {
                                    echo "<option value=''>Error al cargar categorías</option>";
                                }
                                ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="subcategoria_id" class="form-label">Subcategoría:</label>
                            <select class="form-select" id="subcategoria_id" name="subcategoria_id">
                                <option value="" disabled selected>Primero seleccione una categoría</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="nombre" class="form-label">Nombre:</label>
                        <input type="text" class="form-control" id="nombre" name="nombre" required>
                    </div>

                    <div class="mb-3">
                        <label for="descripcion" class="form-label">Descripción:</label>
                        <textarea class="form-control" id="descripcion" name="descripcion" rows="3"></textarea>
                    </div>



                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="precio_compra" class="form-label">Precio Compra:</label>
                            <input type="number" class="form-control" id="precio_compra" name="precio_compra" step="0.01" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="precio_venta" class="form-label">Precio Venta:</label>
                            <input type="number" class="form-control" id="precio_venta" name="precio_venta" step="0.01" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="stock_minimo" class="form-label">Stock Mínimo:</label>
                            <input type="number" class="form-control" id="stock_minimo" name="stock_minimo" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="stock_maximo" class="form-label">Stock Máximo:</label>
                            <input type="number" class="form-control" id="stock_maximo" name="stock_maximo" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="unidad_medida" class="form-label">Unidad de Medida:</label>
                            <select class="form-select" id="unidad_medida" name="unidad_medida">
                                <option value="" disabled selected>Seleccione una unidad</option>
                                <option value="unidades">Unidades</option>
                                <option value="litros">Litros</option>
                                <option value="kilogramos">Kilogramos</option>
                                <option value="metros">Metros</option>
                                <option value="milímetros">Milímetros</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="ubicacion_almacen" class="form-label">Ubicación Almacén:</label>
                            <input type="text" class="form-control" id="ubicacion_almacen" name="ubicacion_almacen" value="Temuco" readonly>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="es_original" class="form-label">Es Original:</label>
                            <select class="form-select" id="es_original" name="es_original">
                                <option value="1">Sí</option>
                                <option value="0">No</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="fabricante" class="form-label">Fabricante:</label>
                            <input type="text" class="form-control" id="fabricante" name="fabricante">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="pais_origen" class="form-label">País de Origen:</label>
                            <input type="text" class="form-control" id="pais_origen" name="pais_origen">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="codigo_fabricante" class="form-label">Código Fabricante:</label>
                            <input type="text" class="form-control" id="codigo_fabricante" name="codigo_fabricante">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="imagen" class="form-label">Imagen del Repuesto:</label>
                        <input type="file" class="form-control" id="imagen" name="imagen" accept="image/*">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancelar
                </button>
                <button type="submit" class="btn btn-primary" form="editForm">
                    <i class="fas fa-save me-1"></i>Guardar cambios
                </button>
            </div>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/inventory.js?v=<?php echo time(); ?>"></script>

<!-- Los estilos para el autocompletado ahora están en styles/autocomplete.css -->



<!-- Modal de asignación -->
<div class="modal fade" id="assignModal" tabindex="-1" aria-labelledby="assignModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignModalLabel">
                    <i class="fas fa-plus-circle me-2"></i>Asignar Compatibilidades
                </h5>
                <!-- <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button> -->
            </div>
            <div class="modal-body">
                <form id="assignForm">
                    <input type="hidden" id="assignProductId" name="repuesto_id">

                    <div class="table-responsive">
                        <table id="compatibilityTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAll" onclick="toggleAllCheckboxes()">
                                            <label class="form-check-label" for="selectAll">Todos</label>
                                        </div>
                                    </th>
                                    <th>Marca</th>
                                    <th>Modelo</th>
                                    <th>Versión</th>
                                    <th>Año</th>
                                    <th>Motor</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                try {
                                    $stmt = $conn->query("SELECT
                                        vc.id,
                                        m.nombre as marca,
                                        mo.nombre as modelo,
                                        vc.version,
                                        CONCAT(vc.anio_inicio, '-', vc.anio_fin) as anios,
                                        CONCAT(vc.cilindrada, 'cc ', vc.tipo_motor) as motor
                                    FROM vehiculo_compatible vc
                                    JOIN modelo mo ON vc.modelo_id = mo.id
                                    JOIN marca m ON mo.marca_id = m.id
                                    ORDER BY m.nombre, mo.nombre, vc.version");

                                    while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                                        echo '<tr>
                                            <td>
                                                <div class="form-check">
                                                    <input class="form-check-input compat-check" type="checkbox" value="' . $row['id'] . '">
                                                </div>
                                            </td>
                                            <td>' . htmlspecialchars($row['marca']) . '</td>
                                            <td>' . htmlspecialchars($row['modelo']) . '</td>
                                            <td>' . htmlspecialchars($row['version']) . '</td>
                                            <td>' . htmlspecialchars($row['anios']) . '</td>
                                            <td>' . htmlspecialchars($row['motor']) . '</td>
                                        </tr>';
                                    }
                                } catch(Exception $e) {
                                    echo '<tr><td colspan="6">Error al cargar vehículos: ' . $e->getMessage() . '</td></tr>';
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancelar
                </button>
                <button type="button" class="btn btn-primary" onclick="saveSelectedCompatibilities()">
                    <i class="fas fa-save me-1"></i>Guardar Compatibilidades
                </button>
            </div>
        </div>
    </div>
</div>

<script src="js/simple-table.js?v=<?php echo time(); ?>"></script>
<script src="js/image-viewer.js?v=<?php echo time(); ?>"></script>

<!-- Estilos adicionales para imágenes en la tabla -->
<style>
    /* Estilo para las imágenes en miniatura en la tabla */
    .table-image {
        width: 60px;
        height: 60px;
        object-fit: contain;
        border-radius: 4px;
        border: 1px solid #eee;
        background-color: #f8f9fa;
    }

    /* Asegurar que la columna de imagen tenga un ancho fijo */
    #tabla_repuestos td:first-child {
        width: 80px;
        text-align: center;
    }

    /* Estilo para cuando no hay imagen disponible */
    .table-image[src="images/no-image.png"] {
        opacity: 0.5;
    }

    /* Contenedor para la imagen en la tabla */
    .table-image-container {
        position: relative;
        display: inline-block;
        cursor: pointer;
    }

    /* Icono de lupa para ampliar imagen */
    .image-zoom-icon {
        position: absolute;
        top: 5px;
        right: 5px;
        background-color: rgba(0, 0, 0, 0.6);
        color: white;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 10;
        font-size: 10px;
    }

    .table-image-container:hover .image-zoom-icon {
        opacity: 1;
    }
</style>

<!-- Incluir el script de autocompletado -->
<script src="js/autocomplete.js?v=<?php echo time(); ?>"></script>

<!-- Script para inicializar el autocompletado en los campos SKU -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando autocompletado para campos SKU...');

    // Inicializar para el campo SKU en el modal de entrada
    setTimeout(function() {
        initSKUAutocomplete();
    }, 500);

    // Mostrar un mensaje de depuración en la consola
    console.log('Autocompletado inicializado correctamente');
});
</script>
</body>
</html>
