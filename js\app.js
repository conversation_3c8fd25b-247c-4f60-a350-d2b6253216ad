// Estado inicial
let cart = [];

// Funciones del carrito
function updateCartCount() {
    const cartCount = document.querySelector(".cart-count");
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    cartCount.textContent = totalItems;
}

async function processSale() {
    try {
        if (!confirm('¿Está seguro de finalizar la venta?')) {
            return;
        }

        const clientId = document.getElementById('client_id')?.value || null;
        const fechaCompromiso = document.getElementById('fecha_compromiso')?.value || null;

        const saleData = {
            tipo_documento: document.getElementById('document_type').value,
            numero_documento: document.getElementById('document_number').value,
            condiciones_pago: document.getElementById('payment_conditions').value,
            abono: document.getElementById('abono').value || '0',
            cliente_id: clientId, // Puede ser null
            total: cart.reduce((sum, item) => {
                const subtotal = item.precio * item.quantity;
                const discount = item.discountType === 'percentage'
                    ? (subtotal * (item.discountValue / 100))
                    : item.discountValue;
                return sum + (subtotal - discount);
            }, 0),
            detalles: cart.map(item => {
                const subtotal = item.precio * item.quantity;
                const descuento = item.discountType === 'percentage'
                    ? (subtotal * (item.discountValue / 100))
                    : item.discountValue;
                return {
                    repuesto_id: item.id,
                    cantidad: item.quantity,
                    precio_unitario: item.precio,
                    subtotal: subtotal,
                    descuento: descuento,
                    tipo_descuento: item.discountType
                };
            })
        };

        // Solo agregar fecha_compromiso si tiene un valor
        if (fechaCompromiso) {
            saleData.fecha_compromiso = fechaCompromiso;
        }

        // Validar que haya productos en el carrito
        if (saleData.detalles.length === 0) {
            alert('El carrito está vacío. Agregue productos antes de finalizar la venta.');
            return;
        }

        const response = await fetch('process_sale.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(saleData)
        });

        const result = await response.json();
        if (result.status === 'success') {
            alert('Venta procesada exitosamente');
            cart = [];
            updateCartCount();
            renderCart();
            // Limpiar formulario
            document.getElementById('document_type').value = 'boleta';
            document.getElementById('document_number').value = '';
            document.getElementById('payment_conditions').value = '';
            document.getElementById('client_info').innerHTML = '';
            document.getElementById('client_rut').value = '';
            document.getElementById('fecha_compromiso').value = '';
            document.getElementById('abono').value = '0';
            // Cerrar modal
            document.querySelector('.cart-modal').classList.remove('active');
            document.querySelector('.cart-overlay').classList.remove('active');
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        alert('Error al procesar la venta: ' + error.message);
    }
}

function addToCart(productId, quantity, directProduct = null) {
    let product;

    if (directProduct) {
        product = {
            id: productId,
            sku: directProduct.sku || '',
            nombre: directProduct.nombre || 'Producto',
            precio: parseFloat(directProduct.precio_venta) || 0,
            quantity: quantity,
            discountType: 'percentage',
            discountValue: 0
        };
    } else {
        const productElement = document.querySelector(`[data-id="${productId}"]`);
        if (!productElement) return;

        product = {
            id: productId,
            sku: productElement.querySelector('.product-sku')?.textContent.trim() || '',
            nombre: productElement.querySelector('.product-name')?.textContent || 'Producto',
            precio: parseFloat(productElement.querySelector('.product-price')?.textContent.replace('$', '')) || 0,
            quantity: quantity,
            discountType: 'percentage',
            discountValue: 0
        };
    }

    const existingItem = cart.find(item => item.sku === (directProduct ? directProduct.sku : product.sku));
    if (existingItem) {
        existingItem.quantity += quantity;
    } else {
        cart.push(product);
    }

    updateCartCount();
    renderCart();
}

// Función para agregar productos directamente al formulario DTE
function addToDTEForm(productId, quantity, directProduct = null) {
    try {
        let product;

        // Obtener información del producto
        if (directProduct) {
            console.log('Agregando producto directo con ID:', productId, 'Datos:', directProduct);
            product = {
                id: productId,
                sku: directProduct.sku || '',
                nombre: directProduct.nombre || 'Producto',
                precio: parseFloat(directProduct.precio_venta) || 0,
                quantity: quantity
            };
        } else {
            console.log('Buscando producto en el DOM con ID:', productId);
            const productElement = document.querySelector(`[data-id="${productId}"]`);
            if (!productElement) {
                console.error('No se encontró el elemento del producto con ID:', productId);
                return;
            }

            console.log('Elemento del producto encontrado:', productElement);
            const skuElement = productElement.querySelector('.product-sku');
            const nameElement = productElement.querySelector('.product-name');
            const priceElement = productElement.querySelector('.product-price');

            if (!skuElement || !nameElement || !priceElement) {
                console.error('Faltan elementos necesarios en el producto');
                return;
            }

            const sku = skuElement.textContent ? skuElement.textContent.trim() : '';
            const nombre = nameElement.textContent ? nameElement.textContent.trim() : 'Producto';
            let precio = 0;

            if (priceElement.textContent) {
                const priceText = priceElement.textContent.replace('$', '').replace(',', '');
                precio = parseFloat(priceText) || 0;
            }

            console.log('Datos extraídos del producto:', { sku, nombre, precio });
            product = {
                id: productId,
                sku: sku,
                nombre: nombre,
                precio: precio,
                quantity: quantity
            };
        }

        // Verificar que el ID sea válido
        if (!product.id) {
            console.error('El producto no tiene un ID válido:', product);
            if (typeof window.showNotification === 'function') {
                window.showNotification('Error: El producto no tiene un ID válido', 'error');
            }
            return;
        }

        console.log('Producto a agregar al DTE:', product);

        // Verificar que existan los elementos necesarios
        const dteCanvas = document.getElementById('dteCanvas');
        const dteOverlay = document.getElementById('dteOverlay');

        if (!dteCanvas || !dteOverlay) {
            console.error('No se encontraron los elementos del canvas DTE');
            return;
        }

        // Buscar si ya existe un ítem con el mismo nombre en el formulario
        const itemsContainer = document.getElementById('itemsContainer');
        if (!itemsContainer) {
            console.error('No se encontró el contenedor de ítems');
            return;
        }

        const existingItems = itemsContainer.querySelectorAll('.item-row');
        let itemExists = false;

        existingItems.forEach(item => {
            const nombreInput = item.querySelector('.item-nombre');
            if (nombreInput && nombreInput.value === product.nombre) {
                // Si existe, actualizar la cantidad
                const cantidadInput = item.querySelector('.item-cantidad');
                if (cantidadInput) {
                    const nuevaCantidad = parseInt(cantidadInput.value) + product.quantity;
                    cantidadInput.value = nuevaCantidad;

                    // Recalcular el monto del ítem
                    if (typeof calcularMontoItem === 'function') {
                        calcularMontoItem(cantidadInput);
                    }

                    if (typeof actualizarMontoNeto === 'function') {
                        actualizarMontoNeto();
                    }

                    // Actualizar el contador DTE cuando se cambia la cantidad
                    updateDTECount();

                    itemExists = true;
                }
            }
        });

        // Si no existe, crear un nuevo ítem
        if (!itemExists) {
            // Verificar si hay un ítem por defecto vacío que se pueda eliminar
            const defaultItems = Array.from(itemsContainer.querySelectorAll('.item-row')).filter(item => {
                const nombreInput = item.querySelector('.item-nombre');
                const precioInput = item.querySelector('.item-precio');
                return (!nombreInput || !nombreInput.value.trim()) &&
                       (!precioInput || parseFloat(precioInput.value) === 0);
            });

            // Si encontramos un ítem por defecto vacío y es el único, lo eliminamos
            if (defaultItems.length === 1 && itemsContainer.querySelectorAll('.item-row').length === 1) {
                itemsContainer.innerHTML = ''; // Limpiar el contenedor
            }

            // Obtener el contador actual de ítems (después de posiblemente eliminar el ítem por defecto)
            const itemCount = itemsContainer.querySelectorAll('.item-row').length;

            // Crear un nuevo elemento para el ítem
            const newItem = document.createElement('div');
            newItem.className = 'item-row';

            // Asegurarse de que el ID del producto sea un valor válido
            const repuestoId = product.id || '';
            console.log('Configurando item-row con repuesto_id:', repuestoId);

            // Agregar el ID del producto como atributo data
            if (repuestoId) {
                newItem.setAttribute('data-repuesto-id', repuestoId);
                console.log('Atributo data-repuesto-id configurado:', newItem.getAttribute('data-repuesto-id'));
            }

            newItem.innerHTML = `
                <button type="button" class="remove-item-btn"><i class="fas fa-times"></i></button>
                ${repuestoId ? `<input type="hidden" class="repuesto-id" value="${repuestoId}">` : ''}
                <div class="form-row">
                    <div class="form-group">
                        <label for="nombre_${itemCount}">Nombre producto</label>
                        <input type="text"
                               id="nombre_${itemCount}"
                               class="modern-input item-nombre"
                               required
                               value="${product.nombre}"
                               pattern="[^:\/&quot;&,@]*"
                               oninput="validateProductField(this)"
                               title="No se permiten los caracteres: : / & &#34; , @">
                    </div>
                    <div class="form-group">
                        <label for="cantidad_${itemCount}">Cantidad</label>
                        <input type="number" id="cantidad_${itemCount}" class="modern-input item-cantidad" required value="${product.quantity}"
                            min="1" step="1" onchange="calcularMontoItem(this); actualizarMontoNeto(); updateDTECount();">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="descripcion_${itemCount}">Descripción producto</label>
                        <input type="text"
                               id="descripcion_${itemCount}"
                               class="modern-input item-descripcion"
                               value="SKU: ${product.sku}"
                               pattern="[^:\/&quot;&,@]*"
                               oninput="validateProductField(this)"
                               title="No se permiten los caracteres: : / & &#34; , @">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="unidad_${itemCount}">Unidad</label>
                        <input type="text" id="unidad_${itemCount}" class="modern-input item-unidad" value="un">
                    </div>
                    <div class="form-group">
                        <label for="precio_${itemCount}">Precio</label>
                        <input type="number" id="precio_${itemCount}" class="modern-input item-precio" required value="${product.precio}"
                            min="0" step="1" onchange="calcularMontoItem(this); actualizarMontoNeto();">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="descuento_${itemCount}">Descuento</label>
                        <input type="number" id="descuento_${itemCount}" class="modern-input item-descuento" value="0"
                            min="0" step="1" onchange="calcularMontoItem(this); actualizarMontoNeto();">
                    </div>
                    <div class="form-group">
                        <label for="recargo_${itemCount}">Recargo</label>
                        <input type="number" id="recargo_${itemCount}" class="modern-input item-recargo" value="0"
                            min="0" step="1" onchange="calcularMontoItem(this); actualizarMontoNeto();">
                    </div>
                    <div class="form-group">
                        <label for="montoItem_${itemCount}">Monto Item</label>
                        <input type="number" id="montoItem_${itemCount}" class="modern-input item-monto" value="0" readonly>
                    </div>
                </div>
            `;

            // Verificar que el ID se haya configurado correctamente
            console.log('Item creado con data-repuesto-id:', newItem.getAttribute('data-repuesto-id'));

            // Agregar el nuevo ítem al contenedor
            itemsContainer.appendChild(newItem);

            // Calcular el monto del ítem
            const cantidadInput = newItem.querySelector('.item-cantidad');
            if (cantidadInput && typeof calcularMontoItem === 'function') {
                calcularMontoItem(cantidadInput);
            }

            if (typeof actualizarMontoNeto === 'function') {
                actualizarMontoNeto();
            }

            // Agregar funcionalidad para eliminar el ítem
            const removeBtn = newItem.querySelector('.remove-item-btn');
            if (removeBtn) {
                removeBtn.addEventListener('click', function() {
                    if (itemsContainer.querySelectorAll('.item-row').length > 1) {
                        itemsContainer.removeChild(newItem);
                        if (typeof actualizarMontoNeto === 'function') {
                            actualizarMontoNeto(); // Actualizar el total al eliminar un ítem
                        }
                        // Actualizar el contador de elementos DTE
                        updateDTECount();
                    } else {
                        alert('Debe mantener al menos un ítem en el documento');
                    }
                });
            }
        }

        // Actualizar el contador de elementos DTE
        updateDTECount();

        // Mostrar notificación
        const totalItems = getItemCount();
        if (typeof window.showNotification === 'function') {
            window.showNotification(`Producto agregado (${totalItems} elementos)`, 'info');
        }
    } catch (error) {
        console.error('Error al agregar producto al formulario DTE:', error);
        if (typeof window.showNotification === 'function') {
            window.showNotification('Ocurrió un error al agregar el producto al formulario DTE', 'error');
        } else {
            alert('Ocurrió un error al agregar el producto al formulario DTE');
        }
    }
}

// Función para obtener el número de elementos en el formulario DTE
function getItemCount() {
    const itemsContainer = document.getElementById('itemsContainer');
    if (!itemsContainer) {
        return 0;
    }

    // Obtener todas las filas de ítems
    const itemRows = itemsContainer.querySelectorAll('.item-row');

    // Si no hay filas, devolver 0
    if (itemRows.length === 0) return 0;

    // Sumar las cantidades de todos los productos
    let totalItems = 0;

    // Método directo: buscar todos los inputs con id que comienza con 'cantidad_'
    const cantidadInputs = document.querySelectorAll('#itemsContainer input[id^="cantidad_"]');

    if (cantidadInputs.length > 0) {
        cantidadInputs.forEach((input) => {
            if (input && input.value) {
                const cantidad = parseInt(input.value) || 0;
                totalItems += cantidad;
            }
        });
    }

    // Actualizar el contador en el DOM directamente para asegurar sincronización
    const dteCount = document.querySelector('.dte-count');
    if (dteCount) {
        dteCount.textContent = totalItems;
        dteCount.style.visibility = totalItems > 0 ? 'visible' : 'hidden';
    }

    return totalItems;
}

// Función para actualizar el contador de elementos DTE
function updateDTECount() {
    const dteCount = document.querySelector('.dte-count');
    if (!dteCount) {
        return;
    }

    const itemCount = getItemCount();

    // Actualizar el contador
    dteCount.textContent = itemCount;

    // Ocultar el contador si es 0
    if (itemCount === 0) {
        dteCount.style.visibility = 'hidden';
    } else {
        dteCount.style.visibility = 'visible';

        // Agregar animación solo si hay elementos
        dteCount.classList.remove('updated');
        void dteCount.offsetWidth; // Forzar reflow para reiniciar la animación
        dteCount.classList.add('updated');
    }
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    updateCartCount();
    renderCart();
}

function renderCart() {
    const cartItems = document.querySelector('.cart-items');
    const cartHTML = cart.map(item => {
        const subtotal = Number(item.precio) * item.quantity;
        const discountAmount = item.discountType === 'percentage'
            ? (subtotal * (item.discountValue / 100))
            : item.discountValue;
        const finalSubtotal = subtotal - discountAmount;

        return `
        <div class="cart-item" data-id="${item.id}">
            <button class="delete-item-btn" onclick="removeFromCart(${item.id})"><i class="fas fa-trash"></i></button>
            <div class="cart-item-details">
                <h4>${item.nombre}</h4>
                <p>SKU: ${item.sku}</p>
                <p class="cantidad-destacada">Cantidad: ${item.quantity}</p>
                <p>Precio unitario: $${Number(item.precio).toFixed(2)}</p>
                <div class="discount-controls">
                    <select class="discount-type modern-select" style="width: 60px;" onchange="updateDiscount('${item.id}', this.value, ${item.discountValue})">
                        <option value="percentage" ${item.discountType === 'percentage' ? 'selected' : ''}>%</option>
                        <option value="fixed" ${item.discountType === 'fixed' ? 'selected' : ''}>$</option>
                    </select>
                    <input type="number" min="0" class="discount-value modern-input" style="width: 80px;" value="${item.discountValue}"
                        onchange="updateDiscount('${item.id}', '${item.discountType}', this.value)">
                </div>
                <p>Subtotal: $${Math.round(subtotal).toLocaleString('es-CL')}</p>
                <p>Descuento: $${Math.round(discountAmount).toLocaleString('es-CL')}</p>
                <p>Total con descuento: $${Math.round(finalSubtotal).toLocaleString('es-CL')}</p>
            </div>
        </div>
    `}).join('');

    cartItems.innerHTML = cartHTML;

    const total = cart.reduce((sum, item) => {
        const subtotal = Number(item.precio) * item.quantity;
        const discount = item.discountType === 'percentage'
            ? (subtotal * (item.discountValue / 100))
            : item.discountValue;
        return sum + (subtotal - discount);
    }, 0);

    document.querySelector('.cart-total').innerHTML = `
        <h3>Total: $${Math.round(total).toLocaleString('es-CL')}</h3>
    `;
}


// Actualizar las funciones del carrito
function updateCartItem(productId, newQuantity) {
    const item = cart.find(item => item.id === productId);
    if (!item) return;

    if (newQuantity <= 0) {
        removeFromCart(productId);
    } else {
        item.quantity = newQuantity;
        updateCartCount();
        renderCart();
    }
}

function updateDiscount(productId, discountType, discountValue) {
    const item = cart.find(item => item.id === productId);
    if (!item) return;

    item.discountType = discountType;
    item.discountValue = Number(discountValue);
    renderCart();
}

// Función para forzar recarga de recursos
function forceReload(url) {
    return url + (url.includes('?') ? '&' : '?') + 'v=' + new Date().getTime();
}

// Event Listeners
// Función para mostrar notificaciones
function showNotification(message, type = 'info') {
    // Check if we already have a notification container
    let notificationContainer = document.getElementById('notification-container');

    if (!notificationContainer) {
        // Create container for notifications if it doesn't exist
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'notification-container';
        notificationContainer.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999;';
        document.body.appendChild(notificationContainer);
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div style="background: ${type === 'error' ? '#ffebee' : '#e8f5e9'};
                    border-left: 4px solid ${type === 'error' ? '#f44336' : '#4caf50'};
                    padding: 15px; margin-bottom: 15px; border-radius: 4px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.2); position: relative;
                    max-width: 350px; word-wrap: break-word; white-space: pre-wrap;">
            <span style="position: absolute; right: 10px; top: 5px; cursor: pointer;
                         font-size: 18px; color: #999;" onclick="this.parentNode.parentNode.remove()">×</span>
            <strong>${type === 'error' ? 'Error' : 'Información'}</strong>
            <p style="margin: 8px 0 0 0;">${message}</p>
        </div>
    `;

    // Add to container
    notificationContainer.appendChild(notification);

    // Remove after some time
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000); // 5 seconds
}

document.addEventListener('DOMContentLoaded', () => {
    // Forzar recarga de recursos dinámicos
    document.querySelectorAll('link[rel="stylesheet"]').forEach(link => {
        link.href = forceReload(link.href);
    });

    // Filter toggle functionality
    const filterToggle = document.querySelector(".filter-toggle");
    const searchBar = document.querySelector(".search-bar");

    if (filterToggle && searchBar) {
        filterToggle.addEventListener("click", () => {
            searchBar.classList.toggle("collapsed");
        });
    }

    // Inicializar el contador DTE y asegurarse de que esté oculto si no hay elementos
    setTimeout(() => {
        updateDTECount();

        // Agregar evento para actualizar el contador cuando se cambia la cantidad de un producto
        const itemsContainer = document.getElementById('itemsContainer');
        if (itemsContainer) {
            // Usar delegación de eventos para capturar cambios en cualquier input de cantidad
            itemsContainer.addEventListener('change', (e) => {
                // Capturar cualquier input que tenga un ID que comience con 'cantidad_'
                if (e.target.id && e.target.id.startsWith('cantidad_')) {
                    updateDTECount();
                }
            });

            // También agregar un evento input para capturar cambios en tiempo real
            itemsContainer.addEventListener('input', (e) => {
                if (e.target.id && e.target.id.startsWith('cantidad_')) {
                    updateDTECount();
                }
            });

            // Agregar delegación de eventos para los botones de eliminar
            itemsContainer.addEventListener('click', (e) => {
                if (e.target.closest('.remove-item-btn')) {
                    // Esperar un momento para que se complete la eliminación
                    setTimeout(() => {
                        updateDTECount();
                    }, 100);
                }
            });
        }

        // Inicializar los botones de eliminar existentes
        document.querySelectorAll('.remove-item-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                setTimeout(() => {
                    updateDTECount();
                }, 100);
            });
        });

        // Agregar evento al botón de agregar item
        const addItemBtn = document.getElementById('addItemBtn');
        if (addItemBtn) {
            addItemBtn.addEventListener('click', () => {
                setTimeout(() => {
                    updateDTECount();
                }, 100);
            });
        }

        // Actualizar el contador una vez al inicio, no es necesario actualizarlo constantemente
        // Solo se actualizará cuando haya cambios en los elementos
    }, 500);

    // Vista toggle
    let currentView = 'grid';
    document.querySelectorAll(".view-btn").forEach(btn => {
        btn.addEventListener("click", () => {
            document.querySelectorAll(".view-btn").forEach(b => b.classList.remove("active"));
            btn.classList.add("active");
            currentView = btn.dataset.view;

            const gridView = document.querySelector(".products-grid");
            const tableView = document.querySelector(".products-table");

            if (currentView === "grid") {
                tableView.style.display = "none";
                gridView.style.display = "grid";
            } else {
                gridView.style.display = "none";
                tableView.style.display = "block";
            }
        });
    });

    // Manejo de cantidades y carrito
   document.getElementById("products-container")?.addEventListener("click", (e) => {
    console.log("Click detected on:", e.target);

    // Seleccionar la tarjeta o la fila de la tabla
    const card = e.target.closest(".product-card");
    const row = e.target.closest("tr");
    const element = card || row;
    if (!element) return;

    console.log("Element selected:", element);

    // Obtener el ID del producto
    const productId = element.getAttribute('data-id');
    if (!productId) {
        console.error("No se pudo obtener el ID del producto:", element);
        return;
    }

    console.log("Product ID:", productId, "Elemento:", element);

    // Buscar el elemento que muestra la cantidad
    let quantityDisplay;
    if (card) {
        // Vista de tarjetas
        quantityDisplay = element.querySelector(".quantity-display");
    } else if (row) {
        // Vista de tabla
        quantityDisplay = element.querySelector(".quantity-display");
    }
    if (!quantityDisplay) return;

    let quantity = parseInt(quantityDisplay.textContent);

    // Manejar clic en el botón menos
    if (e.target.classList.contains("minus")) {
        console.log("Minus button clicked");
        quantity = Math.max(0, quantity - 1);
        quantityDisplay.textContent = quantity;
    }

    // Manejar clic en el botón más
    if (e.target.classList.contains("plus")) {
        console.log("Plus button clicked");
        quantity = Math.min(99, quantity + 1);
        quantityDisplay.textContent = quantity;
    }

    // Manejar clic en el botón de agregar al DTE
    if (e.target.classList.contains("add-to-cart-btn") || e.target.closest(".add-to-cart-btn")) {
        console.log("Add to DTE button clicked");
        if (productId) {
            // Si la cantidad es 0, establecer en 1 automáticamente
            if (quantity === 0) {
                quantity = 1;
                quantityDisplay.textContent = quantity;
            }
            console.log("Agregando producto al DTE con ID:", productId, "Cantidad:", quantity);
            addToDTEForm(productId, quantity);
            quantityDisplay.textContent = "0";
        } else {
            console.error("No se puede agregar el producto al DTE. ID:", productId, "Cantidad:", quantity);
        }
    }
});

    // Manejo del carrito y usuario
    const cartModal = document.querySelector(".cart-modal");
    const cartIcon = document.querySelector(".cart-icon i.fa-shopping-cart");
    const closeCartBtn = document.querySelector(".close-cart");
    const cartOverlay = document.querySelector(".cart-overlay");
    const userIcon = document.querySelector(".fa-user");
    const userDropdown = document.querySelector(".user-dropdown");

    if (cartIcon) {
        cartIcon.addEventListener("click", (e) => {
            e.stopPropagation();
            cartModal.classList.add("active");
            cartOverlay.classList.add("active");
        });
    }

    if (closeCartBtn) {
        closeCartBtn.addEventListener("click", () => {
            cartModal.classList.remove("active");
            cartOverlay.classList.remove("active");
        });
    }

    if (cartOverlay) {
        cartOverlay.addEventListener("click", () => {
            cartModal.classList.remove("active");
            cartOverlay.classList.remove("active");
        });
    }

    if (userIcon) {
        userIcon.addEventListener("click", (e) => {
            e.stopPropagation();
            userDropdown.style.display = userDropdown.style.display === "block" ? "none" : "block";
        });
    }

    document.addEventListener("click", (e) => {
        if (!userDropdown.contains(e.target) && !userIcon.contains(e.target)) {
            userDropdown.style.display = "none";
        }
    });

    // Actualizar el event listener del carrito
    const cartItemsContainer = document.querySelector(".cart-items");

    // Agregar event listeners
    if (cartItemsContainer) {
        cartItemsContainer.addEventListener("click", (e) => {
            const cartItem = e.target.closest(".cart-item");
            if (!cartItem) return;

            const productId = cartItem.dataset.id;
            const item = cart.find(item => item.id === productId);
            if (!item) return;

            if (e.target.classList.contains("delete-item-btn")) {
                removeFromCart(productId);
            } else if (e.target.classList.contains("cart-quantity-btn")) {
                const change = e.target.classList.contains("plus") ? 1 : -1;
                const newQuantity = item.quantity + change;

                if (newQuantity <= 0) {
                    removeFromCart(productId);
                } else {
                    item.quantity = newQuantity;
                    updateCartCount();
                    renderCart();
                }
            }
        });
    }

    const searchButton = document.querySelector(".search-button");
    const searchInput = document.querySelector("#search-input");
    const resetButton = document.querySelector(".reset-button");

    async function fetchProducts(params = new URLSearchParams()) {
        const baseUrl = window.location.origin;
        const url = `get_products.php`;

        try {
            const response = await fetch(`${url}?${params.toString()}`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                mode: 'same-origin',
                credentials: 'same-origin'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            if (data.status === 'success') {
                renderGridView(data.data);
                renderTableView(data.data);

                if (currentView === 'grid') {
                    document.querySelector(".products-grid").style.display = "grid";
                    document.querySelector(".products-table").style.display = "none";
                } else {
                    document.querySelector(".products-grid").style.display = "none";
                    document.querySelector(".products-table").style.display = "block";
                }
            }
        } catch (error) {
            console.error('Error en la petición:', error);
            alert('Error al conectar con el servidor: ' + error.message);
        }
    }

    if (resetButton) {
        resetButton.addEventListener("click", () => {
            // Reset all form inputs
            document.querySelector("#marca_filter").value = "";
            document.querySelector("#modelo_filter").value = "";
            document.querySelector("#anio").value = "";
            document.querySelector("#combustible").value = "";
            document.querySelector("#cilindrada").value = "";
            searchInput.value = "";

            // Fetch all products without filters
            fetchProducts();
        });
    }

    // Función para obtener parámetros de búsqueda
function obtenerParametrosBusqueda() {
    const parametros = {
        search: document.querySelector('#search-input').value,
        marca: document.querySelector('#marca_filter').value,
        modelo: document.querySelector('#modelo_filter').value,
        anio: document.querySelector('#anio').value,
        combustible: document.querySelector('#combustible').value,
        cilindrada: document.querySelector('#cilindrada').value
    };

    // Filtrar parámetros vacíos
    Object.keys(parametros).forEach(key => {
        if (!parametros[key]) delete parametros[key];
    });

    return new URLSearchParams(parametros);
}

// Función para construir URL
function construirURL() {
    const baseUrl = window.location.origin + window.location.pathname;
    const directorio = baseUrl.substring(0, baseUrl.lastIndexOf('/') + 1);
    return directorio + 'get_products.php';
}

// Función para hacer petición fetch
async function buscarProductos(url, params) {
    try {
        console.log('URL de búsqueda:', `${url}?${params}`);
        const response = await fetch(`${url}?${params}`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Cache-Control': 'no-cache'
            }
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Error response:', errorText);
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Datos recibidos:', data);
        return data;
    } catch (error) {
        console.error('Error en buscarProductos:', error);
        throw error;
    }
}

// Manejador del evento click simplificado
if (searchButton) {
    searchButton.addEventListener("click", async () => {
        try {
            console.log('Iniciando búsqueda...');
            const params = new URLSearchParams({
                search: document.querySelector('#search-input').value || '',
                marca: document.querySelector('#marca_filter').value || '',
                modelo: document.querySelector('#modelo_filter').value || '',
                anio: document.querySelector('#anio').value || '',
                combustible: document.querySelector('#combustible').value || '',
                cilindrada: document.querySelector('#cilindrada').value || ''
            });

            console.log('Parámetros de búsqueda:', params.toString());
            const response = await fetch(`get_products.php?${params.toString()}`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.status === 'success') {
                console.log('Productos encontrados:', data.data.length);
                renderGridView(data.data);
                renderTableView(data.data);

                const gridView = document.querySelector(".products-grid");
                const tableView = document.querySelector(".products-table");

                if (currentView === 'grid') {
                    gridView.style.display = "grid";
                    tableView.style.display = "none";
                } else {
                    gridView.style.display = "none";
                    tableView.style.display = "block";
                }
            } else {
                console.error('Error en respuesta:', data);
                alert('No se encontraron productos');
            }
        } catch (error) {
            console.error('Error en búsqueda:', error);
            alert('Error al buscar productos: ' + error.message);
        }
    });
}


    function renderGridView(products) {
        const gridContainer = document.querySelector('.products-grid');
        gridContainer.style.display = 'grid';
        gridContainer.style.gridTemplateColumns = 'repeat(auto-fit, minmax(250px, 1fr))';
        gridContainer.style.gap = '1.5rem';
        gridContainer.innerHTML = products.map(product => `
            <div class="product-card" data-id="${product.id || ''}">
                <img src="${product.url_imagen}"
                     alt="${product.nombre}"
                     class="product-image">
                <div class="product-info">
                    <div class="product-details">
                        <div class="detail-row">
                            <span class="detail-label">SKU:</span>
                            <span class="product-sku">${product.sku || 'N/A'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Nombre:</span>
                            <span class="product-name">${product.nombre || 'N/A'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Marca:</span>
                            <span class="product-brand">${product.marca_nombre || 'N/A'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Modelo:</span>
                            <span class="product-model">${product.modelo_nombre || 'N/A'}</span>
                        </div>

                        <div class="detail-row">
                            <span class="detail-label">Año:</span>
                            <span class="product-model">${product.anio || 'N/A'}</span>
                        </div>

                        <div class="detail-row">
                            <span class="detail-label">Combustible:</span>
                            <span class="product-fuel">${product.combustible || 'N/A'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Cilindrada:</span>
                            <span class="product-engine">${product.cilindrada || 'N/A'}</span>
                        </div>
                        <div class="detail-row">
                            <p class="product-description">${product.descripcion || ''}</p>
                        </div>
                        <p class="product-price">$${parseFloat(product.precio_venta || 0).toFixed(2)}</p>
                    </div>
                    <div class="controls-container">
                        <div class="quantity-controls">
                            <button class="quantity-btn minus">-</button>
                            <span class="quantity-display">0</span>
                            <button class="quantity-btn plus">+</button>
                        </div>
                        <button class="add-to-cart-btn">Agregar a DTE</button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    function renderTableView(products) {
        const tableContainer = document.querySelector('.products-table tbody');
        tableContainer.innerHTML = products.map(product => `
            <tr data-id="${product.id || ''}">
                <td>${product.sku || 'N/A'}</td>
                <td>${product.nombre || 'N/A'}</td>
                <td>${product.codigo_fabricante || 'N/A'}</td>
                <td>${product.ubicacion_tienda || 'N/A'}</td>
                <td>${product.marca_nombre || 'N/A'}</td>
                <td>${product.modelo_nombre || 'N/A'}</td>
                <td>${product.anio || 'N/A'}</td>
                <td>${parseFloat(product.precio_venta || 0).toFixed(2)}</td>
                <td>
                    <div class="quantity-controls">
                        <button class="quantity-btn minus">-</button>
                        <span class="quantity-display">0</span>
                        <button class="quantity-btn plus">+</button>
                    </div>
                    <button class="add-to-cart-btn">Agregar a DTE</button>
                </td>
            </tr>
        `).join('');
    }
});

let fullDataset = [];

document.addEventListener('DOMContentLoaded', async () => {
    await loadFullDataset();
    setupFilterListeners();
});

async function loadFullDataset() {
    const response = await fetch('get_vehiculos_full.php');
    fullDataset = await response.json();
}

function setupFilterListeners() {
    const marcaSelect = document.getElementById('marca_filter');
    const modeloSelect = document.getElementById('modelo_filter');
    const anioSelect = document.getElementById('anio');
    const combustibleSelect = document.getElementById('combustible');
    const cilindradaSelect = document.getElementById('cilindrada');

    if (marcaSelect) {
        marcaSelect.addEventListener('change', () => updateFilters('marca_filter'));
    }

    if (modeloSelect) {
        modeloSelect.addEventListener('change', () => updateFilters('modelo'));
    }

    if (anioSelect) {
        anioSelect.addEventListener('change', () => updateFilters('anio'));
    }

    if (combustibleSelect) {
        combustibleSelect.addEventListener('change', () => updateFilters('combustible'));
    }
}

function updateFilters(changedFilter) {
    const selectedMarca = document.getElementById('marca_filter').value;
    const selectedModelo = document.getElementById('modelo_filter').value;
    const selectedAnio = document.getElementById('anio').value;
    const selectedCombustible = document.getElementById('combustible').value;

    let filteredData = fullDataset;

    // Apply filters sequentially
    if (selectedMarca) {
        filteredData = filteredData.filter(item => item.marca_id == selectedMarca);
    }

    if (selectedModelo) {
        filteredData = filteredData.filter(item => item.modelo_id == selectedModelo);
    }

    if (selectedAnio) {
        filteredData = filteredData.filter(item =>
            item.anio_inicio <= selectedAnio && item.anio_fin >= selectedAnio
        );
    }

    if (selectedCombustible) {
        filteredData = filteredData.filter(item =>
            item.combustible === selectedCombustible
        );
    }

    // Update dropdowns based on filtered data
    updateDropdownOptions(filteredData, changedFilter);
}

function updateDropdownOptions(filteredData, changedFilter) {
    if (changedFilter !== 'modelo') {
        updateModeloOptions(filteredData);
    }
    if (changedFilter !== 'combustible') {
        updateCombustibleOptions(filteredData);
    }
    if (changedFilter !== 'cilindrada') {
        updateCilindradaOptions(filteredData);
    }
    if (changedFilter !== 'anio') {
        updateAnioOptions(filteredData);
    }
}

function updateModeloOptions(filteredData) {
    const modeloSelect = document.getElementById('modelo_filter');
    const currentValue = modeloSelect.value;

    const modelosMap = new Map();
    filteredData.forEach(item => {
        modelosMap.set(item.modelo_id, {
            id: item.modelo_id,
            nombre: item.modelo_nombre
        });
    });

    const uniqueModelos = Array.from(modelosMap.values())
        .sort((a, b) => a.nombre.localeCompare(b.nombre));

    modeloSelect.innerHTML = '<option value="">Modelo</option>';
    uniqueModelos.forEach(modelo => {
        const option = document.createElement('option');
        option.value = modelo.id;
        option.textContent = modelo.nombre;
        modeloSelect.appendChild(option);
    });

    if (uniqueModelos.find(m => m.id == currentValue)) {
        modeloSelect.value = currentValue;
    }
}

function updateCombustibleOptions(filteredData) {
    const combustibleSelect = document.getElementById('combustible');
    const currentValue = combustibleSelect.value;

    const combustiblesMap = new Map();
    filteredData.forEach(item => {
        if (item.combustible) {
            combustiblesMap.set(item.combustible, item.combustible);
        }
    });

    const uniqueCombustibles = Array.from(combustiblesMap.values()).sort();

    combustibleSelect.innerHTML = '<option value="">Seleccione</option>';
    uniqueCombustibles.forEach(combustible => {
        const option = document.createElement('option');
        option.value = combustible;
        option.textContent = combustible;
        combustibleSelect.appendChild(option);
    });

    if (uniqueCombustibles.includes(currentValue)) {
        combustibleSelect.value = currentValue;
    }
}

function updateCilindradaOptions(filteredData) {
    const cilindradaSelect = document.getElementById('cilindrada');
    const currentValue = cilindradaSelect.value;

    const cilindradasMap = new Map();
    filteredData.forEach(item => {
        if (item.cilindrada) {
            cilindradasMap.set(item.cilindrada, item.cilindrada);
        }
    });

    const uniqueCilindradas = Array.from(cilindradasMap.values())
        .sort((a, b) => a - b);

    cilindradaSelect.innerHTML = '<option value="">Cilindrada</option>';
    uniqueCilindradas.forEach(cilindrada => {
        const option = document.createElement('option');
        option.value = cilindrada;
        option.textContent = cilindrada;
        cilindradaSelect.appendChild(option);
    });

    if (uniqueCilindradas.includes(parseInt(currentValue))) {
        cilindradaSelect.value = currentValue;
    }
}

function updateAnioOptions(filteredData) {
    const anioSelect = document.getElementById('anio');
    const currentValue = anioSelect.value;

    const aniosSet = new Set();
    filteredData.forEach(item => {
        for(let year = item.anio_inicio; year <= item.anio_fin; year++) {
            aniosSet.add(year);
        }
    });

    const uniqueAnios = Array.from(aniosSet).sort((a, b) => b - a);

    anioSelect.innerHTML = '<option value="">Año</option>';
    uniqueAnios.forEach(anio => {
        const option = document.createElement('option');
        option.value = anio;
        option.textContent = anio;
        anioSelect.appendChild(option);
    });

    if (uniqueAnios.includes(parseInt(currentValue))) {
        anioSelect.value = currentValue;
    }
}
document.getElementById('search-realtime')?.addEventListener('input', function(e) {
    const searchValue = e.target.value;

    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
        if (!searchValue.trim()) return;

        const params = new URLSearchParams({
            search: searchValue
        });

        try {
            fetch('get_repuestos_SKU.php?' + params.toString(), {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                }
            })
            .then(async response => {
                if (!response.ok) {
                    // Intentamos obtener el mensaje de error del servidor
                    const errorText = await response.text();
                    console.log('Error Response:', errorText);
                    throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Datos recibidos:', data);
                if (data.status === 'success' && data.data && data.data.length > 0) {
                    const product = data.data[0];
                    if (product && product.id) {
                        addToDTEForm(product.id, 1, product);
                    } else {
                        console.error('Producto inválido:', product);
                        alert('El producto encontrado no tiene un ID válido');
                    }
                } else {
                    alert('No se encontraron productos con ese SKU');
                }
                const searchInput = document.getElementById('search-realtime');
                if (searchInput) searchInput.value = '';
            })
            .catch(error => {
                console.error('Error completo:', error);
                alert('El SKU ingresado no se encuentra en la base de datos');
                const searchInput = document.getElementById('search-realtime');
                if (searchInput) searchInput.value = '';
            });
        } catch (error) {
            console.error('Error al buscar producto por SKU:', error);
            alert('Ocurrió un error al buscar el producto');
        }
    }, 300);
});


// User dropdown functionality
async function checkClient() {
    const rutInput = document.getElementById('client_rut');
    const clientInfo = document.getElementById('client_info');

    try {
        const response = await fetch(`check_client.php?rut=${rutInput.value}`);
        const data = await response.json();

        if (data.exists) {
            clientInfo.innerHTML = `
                <input type="text" value="${data.data.nombre}" readonly class="checkout-input">
                <input type="hidden" id="client_id" value="${data.data.cliente_id}">
            `;
        } else {
            clientInfo.innerHTML = `
                <button onclick="showNewClientForm('${rutInput.value}')" class="checkout-btn" style="margin-bottom: 10px;">
                    <i class="fas fa-plus"></i> Agregar Nuevo Cliente
                </button>
            `;
        }
    } catch (error) {
        alert('Error al verificar cliente');
    }
}

function showNewClientForm(rut) {
    const form = document.getElementById('newClientForm');
    document.getElementById('new_client_rut').value = rut;
    form.style.display = 'block';
}

async function saveNewClient() {
    const clientData = {
        nombre: document.getElementById('new_client_nombre').value,
        rut: document.getElementById('new_client_rut').value,
        direccion: document.getElementById('new_client_direccion').value,
        telefono: document.getElementById('new_client_telefono').value
    };

    try {
        const response = await fetch('save_client.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(clientData)
        });

        const result = await response.json();

        if (result.status === 'success') {
            // Ocultar el formulario
            document.getElementById('newClientForm').style.display = 'none';

            // Mostrar la información del cliente
            const clientInfo = document.getElementById('client_info');
            clientInfo.innerHTML = `
                <div class="success-message" style="background-color: #dff0d8; color: #3c763d; padding: 10px; border: 1px solid #d6e9c6; border-radius: 4px; margin-bottom: 10px;">
                    Cliente creado exitosamente
                </div>
                <input type="text" value="${clientData.nombre}" readonly class="checkout-input">
                <input type="hidden" id="client_id" value="${result.cliente_id}">
            `;

            // Remover el mensaje después de 3 segundos
            setTimeout(() => {
                const successMessage = clientInfo.querySelector('.success-message');
                if (successMessage) {
                    successMessage.style.transition = 'opacity 0.5s ease';
                    successMessage.style.opacity = '0';
                    setTimeout(() => successMessage.remove(), 500);
                }
            }, 3000);
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('Error al guardar cliente:', error);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const userIcon = document.querySelector('.fa-user');
    const dropdown = document.querySelector('.user-dropdown');
    let isDropdownOpen = false;

    if (userIcon) {
        userIcon.addEventListener('click', function(e) {
            e.stopPropagation();
            isDropdownOpen = !isDropdownOpen;
            if (isDropdownOpen) {
                dropdown.style.display = 'block';
                dropdown.classList.add('active');
            } else {
                dropdown.style.display = 'none';
                dropdown.classList.remove('active');
            }
        });
    }

    document.addEventListener('click', function(e) {
        if (!dropdown.contains(e.target) && !userIcon.contains(e.target)) {
            dropdown.style.display = 'none';
            dropdown.classList.remove('active');
            isDropdownOpen = false;
        }
    });
});

document.addEventListener('DOMContentLoaded', () => {
    // ...existing code...

    // Add marca_filter event listener with debugging
    const marcaFilter = document.getElementById('marca_filter');
    const modeloFilter = document.getElementById('modelo_filter');

    if (marcaFilter && modeloFilter) {
        console.log('marca_filter and modelo_filter elements found');
        marcaFilter.addEventListener('change', async function() {
            const marcaId = this.value;
            console.log('Marca seleccionada:', marcaId);

            try {
                modeloFilter.innerHTML = '<option value="">Cargando...</option>';
                modeloFilter.disabled = true;

                if (!marcaId) {
                    console.log('No marca selected, resetting modelo filter');
                    modeloFilter.innerHTML = '<option value="">Modelo</option>';
                    modeloFilter.disabled = false;
                    return;
                }

                console.log('Fetching modelos for marca_id:', marcaId);
                const response = await fetch(`get_modelos.php?marca_id=${marcaId}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache'
                    }
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Server response:', errorText);
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('Received data:', data);

                modeloFilter.innerHTML = '<option value="">Modelo</option>';
                if (Array.isArray(data)) {
                    data.forEach(modelo => {
                        console.log('Adding modelo:', modelo);
                        const option = document.createElement('option');
                        option.value = modelo.id;
                        option.textContent = modelo.nombre;
                        modeloFilter.appendChild(option);
                    });
                } else {
                    console.error('Received non-array data:', data);
                }
            } catch (error) {
                console.error('Error completo:', error);
                modeloFilter.innerHTML = '<option value="">Error al cargar modelos</option>';
            } finally {
                modeloFilter.disabled = false;
            }
        });
    } else {
        console.error('No se encontraron los elementos select');
    }

    // ...existing code...
});

// Manejador específico para el filtro de marca y modelo
document.addEventListener('DOMContentLoaded', function() {
    const marcaFilter = document.getElementById('marca_filter');
    const modeloFilter = document.getElementById('modelo_filter');

    if (marcaFilter && modeloFilter) {
        console.log('Inicializando filtros marca/modelo');

        // Limpiar cualquier event listener previo
        marcaFilter.removeEventListener('change', handleMarcaChange);

        // Agregar el nuevo event listener
        marcaFilter.addEventListener('change', handleMarcaChange);
    } else {
        console.error('No se encontraron los elementos de filtro marca/modelo');
    }
});

// Asegurarse de que las peticiones fetch no usen caché
async function fetchWithNoCache(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
    };

    const mergedOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...(options.headers || {})
        }
    };

    return fetch(url, mergedOptions);
}

// Actualizar la función handleMarcaChange para usar fetchWithNoCache
async function handleMarcaChange() {
    const marcaId = this.value;
    const modeloFilter = document.getElementById('modelo_filter');

    console.log('Marca seleccionada:', marcaId);

    try {
        modeloFilter.innerHTML = '<option value="">Cargando...</option>';
        modeloFilter.disabled = true;

        if (!marcaId) {
            console.log('No marca selected, resetting modelo filter');
            modeloFilter.innerHTML = '<option value="">Modelo</option>';
            modeloFilter.disabled = false;
            return;
        }

        console.log('Fetching modelos for marca_id:', marcaId);
        const response = await fetchWithNoCache(`get_modelos.php?marca_id=${marcaId}`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Server response:', errorText);
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Received data:', data);

        modeloFilter.innerHTML = '<option value="">Modelo</option>';
        if (Array.isArray(data)) {
            data.forEach(modelo => {
                console.log('Adding modelo:', modelo);
                const option = document.createElement('option');
                option.value = modelo.id;
                option.textContent = modelo.nombre;
                modeloFilter.appendChild(option);
            });
        } else {
            console.error('Received non-array data:', data);
        }
    } catch (error) {
        console.error('Error completo:', error);
        modeloFilter.innerHTML = '<option value="">Error al cargar modelos</option>';
    } finally {
        modeloFilter.disabled = false;
    }
}

// ...existing code...

// Función separada para manejar el cambio de marca
async function handleMarcaChange() {
    const marcaId = this.value;
    const modeloFilter = document.getElementById('modelo_filter');

    console.log('Marca seleccionada:', marcaId);

    try {
        modeloFilter.innerHTML = '<option value="">Cargando...</option>';
        modeloFilter.disabled = true;

        if (!marcaId) {
            console.log('No marca selected, resetting modelo filter');
            modeloFilter.innerHTML = '<option value="">Modelo</option>';
            modeloFilter.disabled = false;
            return;
        }

        console.log('Fetching modelos for marca_id:', marcaId);
        const response = await fetchWithNoCache(`get_modelos.php?marca_id=${marcaId}`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Server response:', errorText);
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Received data:', data);

        modeloFilter.innerHTML = '<option value="">Modelo</option>';
        if (Array.isArray(data)) {
            data.forEach(modelo => {
                console.log('Adding modelo:', modelo);
                modeloFilter.innerHTML += `<option value="${modelo.id}">${modelo.nombre}</option>`;
            });
        } else {
            console.error('Received non-array data:', data);
        }
    } catch (error) {
        console.error('Error completo:', error);
        modeloFilter.innerHTML = '<option value="">Error al cargar modelos</option>';
    } finally {
        modeloFilter.disabled = false;
    }
}

// ...existing code...
document.addEventListener('DOMContentLoaded', function() {
    const marcaFilter = document.getElementById('marca_filter');
    const modeloFilter = document.getElementById('modelo_filter');

    // Verificar si los elementos existen
    if (!marcaFilter || !modeloFilter) {
        console.error('No se encontraron los elementos de filtro');
        return;
    }

    // Event listener para el cambio de marca
    marcaFilter.addEventListener('change', function() {
        const marcaId = this.value;
        console.log('Marca seleccionada:', marcaId);

        // Resetear y deshabilitar el selector de modelo
        modeloFilter.innerHTML = '<option value="">Cargando...</option>';
        modeloFilter.disabled = true;

        // Si no hay marca seleccionada, resetear el filtro de modelo
        if (!marcaId) {
            console.log('No marca selected, resetting modelo filter');
            modeloFilter.innerHTML = '<option value="">Seleccione un modelo</option>';
            modeloFilter.disabled = false;
            return;
        }

        // Realizar la petición AJAX
        fetch(`get_modelos.php?marca_id=${marcaId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Error en la respuesta del servidor');
                }
                return response.json();
            })
            .then(data => {
                console.log('Datos recibidos:', data);

                // Limpiar el select de modelos
                modeloFilter.innerHTML = '<option value="">Seleccione un modelo</option>';

                // Agregar las nuevas opciones
                data.forEach(modelo => {
                    const option = document.createElement('option');
                    option.value = modelo.id;
                    option.textContent = modelo.nombre;
                    modeloFilter.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error:', error);
                modeloFilter.innerHTML = '<option value="">Error al cargar modelos</option>';
            })
            .finally(() => {
                modeloFilter.disabled = false;
            });
    });
});

document.addEventListener('DOMContentLoaded', function() {
    const marcaFilter = document.getElementById('marca_filter');
    const modeloFilter = document.getElementById('modelo_filter');

    console.log('DOM Cargado, buscando elementos de filtro');
    console.log('Marca Filter:', marcaFilter);
    console.log('Modelo Filter:', modeloFilter);

    if (!marcaFilter || !modeloFilter) {
        console.error('No se encontraron los elementos de filtro');
        return;
    }

    marcaFilter.addEventListener('change', async function() {
        const marcaId = this.value;
        console.log('Cambio detectado en marca, valor:', marcaId);

        try {
            modeloFilter.innerHTML = '<option value="">Cargando...</option>';
            modeloFilter.disabled = true;

            if (!marcaId) {
                console.log('No hay marca seleccionada');
                modeloFilter.innerHTML = '<option value="">Seleccione un modelo</option>';
                modeloFilter.disabled = false;
                return;
            }

            const url = `get_modelos.php?marca_id=${marcaId}`;
            console.log('Realizando petición a:', url);

            const response = await fetch(url);
            console.log('Respuesta recibida:', response);

            const data = await response.json();
            console.log('Datos recibidos:', data);

            modeloFilter.innerHTML = '<option value="">Seleccione un modelo</option>';

            if (Array.isArray(data)) {
                data.forEach(modelo => {
                    console.log('Agregando modelo:', modelo);
                    const option = document.createElement('option');
                    option.value = modelo.id;
                    option.textContent = modelo.nombre;
                    modeloFilter.appendChild(option);
                });
            } else {
                console.error('Los datos recibidos no son un array:', data);
            }
        } catch (error) {
            console.error('Error completo:', error);
            modeloFilter.innerHTML = '<option value="">Error al cargar modelos</option>';
        } finally {
            modeloFilter.disabled = false;
        }
    });

    // Disparar el evento change si hay una marca seleccionada inicialmente
    if (marcaFilter.value) {
        console.log('Marca inicial seleccionada, disparando evento change');
        marcaFilter.dispatchEvent(new Event('change'));
    }
});

function togglePaymentFields() {
    const paymentFields = document.getElementById('payment-fields');
    const button = event.currentTarget;
    const icon = button.querySelector('i');

    if (paymentFields.style.display === 'none' || paymentFields.style.display === '') {
        paymentFields.style.display = 'block';
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
    } else {
        paymentFields.style.display = 'none';
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
    }
}

// ...existing code...

function goToStage2() {
    // Validar que haya productos en el carrito
    if (cart.length === 0) {
        alert('Agregue productos al carrito antes de continuar');
        return;
    }

    document.getElementById('cart-stage-1').classList.remove('active');
    document.getElementById('cart-stage-2').classList.add('active');
}

function goToStage1() {
    document.getElementById('cart-stage-2').classList.remove('active');
    document.getElementById('cart-stage-1').classList.add('active');
}

// Reset stages when closing cart
document.querySelector('.close-cart')?.addEventListener('click', () => {
    setTimeout(() => {
        goToStage1(); // Asegurarse de que el carrito vuelva a la etapa 1 al cerrarlo
    }, 300); // Esperar a que termine la animación de cierre
});

document.querySelector('.cart-overlay')?.addEventListener('click', () => {
    setTimeout(() => {
        goToStage1(); // Asegurarse de que el carrito vuelva a la etapa 1 al cerrarlo
    }, 300); // Esperar a que termine la animación de cierre
});

// ...existing code...
