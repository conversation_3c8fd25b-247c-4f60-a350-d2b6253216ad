/**
 * Herramienta de diagnóstico para resolver problemas con sobres y archivos XML
 */

// Función para ejecutar la herramienta de diagnóstico
function runDiagnosticTool() {
    // Mostrar indicador de carga
    $("#loader").show();
    
    // Limpiar logs anteriores
    clearLogs();
    
    // Mostrar el contenedor de logs
    $("#detailedLogsContainer").show();
    addLogEntry('info', 'Iniciando herramienta de diagnóstico');
    
    // Realizar la petición AJAX al script de diagnóstico
    $.ajax({
        url: 'check_xml_files.php',
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            $("#loader").hide();
            
            if (data.success) {
                // Registrar información del sistema
                addLogEntry('info', 'Información del sistema', data.system_info);
                
                // Registrar estado de directorios
                Object.keys(data.directorios).forEach(function(dir) {
                    const dirInfo = data.directorios[dir];
                    const dirExists = dirInfo.existe === 'Sí';
                    
                    if (dirExists) {
                        addLogEntry('success', `Directorio: ${dir}`, dirInfo);
                    } else {
                        addLogEntry('error', `Directorio no encontrado: ${dir}`, dirInfo);
                    }
                });
                
                // Registrar estado de archivos XML
                addLogEntry('info', `Documentos pendientes: ${data.documentos_pendientes_total}`);
                
                let xmlValidos = 0;
                let xmlNoEncontrados = 0;
                
                data.xml_documentos.forEach(function(xml) {
                    if (xml.existe === 'Sí') {
                        if (xml.es_xml_valido) {
                            xmlValidos++;
                            addLogEntry('success', `XML válido: ID ${xml.id}, Tipo DTE ${xml.tipo_dte}`, xml);
                        } else {
                            addLogEntry('warning', `XML con errores: ID ${xml.id}, Tipo DTE ${xml.tipo_dte}`, xml);
                        }
                    } else {
                        xmlNoEncontrados++;
                        addLogEntry('error', `XML no encontrado: ID ${xml.id}, Tipo DTE ${xml.tipo_dte}`, xml);
                    }
                });
                
                // Mostrar resumen
                let statusType = 'info';
                let statusMessage = '';
                
                if (xmlNoEncontrados > 0) {
                    statusType = 'error';
                    statusMessage = `Diagnóstico completado: ${xmlNoEncontrados} archivos XML no encontrados. Revise las rutas y permisos.`;
                    
                    // Sugerir posibles soluciones
                    const directorioPadre = data.xml_documentos[0]?.directorio_padre || 'N/A';
                    addLogEntry('info', 'Posibles soluciones:', [
                        `1. Verifique que el directorio '${directorioPadre}' existe y tiene permisos adecuados.`,
                        '2. Compruebe que las rutas en la base de datos corresponden a las ubicaciones reales de los archivos.',
                        '3. Asegúrese de que el usuario del servidor web tiene permisos de lectura en los directorios correspondientes.'
                    ]);
                } else if (xmlValidos < data.documentos_pendientes_total) {
                    statusType = 'warning';
                    statusMessage = `Diagnóstico completado: Algunos archivos XML (${data.documentos_pendientes_total - xmlValidos}) tienen errores de formato.`;
                } else {
                    statusType = 'success';
                    statusMessage = `Diagnóstico completado: Todos los archivos XML (${xmlValidos}) son válidos.`;
                }
                
                // Mostrar resultado general
                showMessage(statusType, statusMessage);
                
            } else {
                // Error en la ejecución del diagnóstico
                addLogEntry('error', 'Error en la herramienta de diagnóstico', {
                    error: data.error,
                    file: data.error_file,
                    line: data.error_line
                });
                
                showMessage('error', 'Error al ejecutar el diagnóstico: ' + data.error);
            }
        },
        error: function(xhr, status, error) {
            $("#loader").hide();
            
            addLogEntry('error', 'Error de conexión con el servidor', {
                status: status,
                error: error,
                response: xhr.responseText
            });
            
            showMessage('error', 'Error al conectar con el servidor: ' + error);
        }
    });
}

// Función especializada para formatear detalles de errores en los logs
function formatErrorDetails(errorData) {
    // Si es un objeto simple, mostrarlo como JSON
    if (typeof errorData !== 'object' || errorData === null) {
        return `<pre>${JSON.stringify(errorData, null, 2)}</pre>`;
    }
    
    let html = '<div style="margin: 10px 0; padding: 10px; background: rgba(244,67,54,0.05); border-radius: 4px;">';
    
    // Primero mostramos mensajes y códigos importantes
    if (errorData.message) {
        html += `<div style="font-weight: bold; margin-bottom: 5px;">Mensaje: ${errorData.message}</div>`;
    }
    if (errorData.code) {
        html += `<div>Código: ${errorData.code}</div>`;
    }
    
    // Si hay categorías de error, mostrarlas de forma destacada
    if (errorData.error_categorias && errorData.error_categorias.length > 0) {
        html += '<div style="margin-top: 8px;">Posibles causas: ';
        errorData.error_categorias.forEach(cat => {
            const catColor = {
                'file_not_found': '#e91e63',
                'permission_denied': '#f44336',
                'memory_limit': '#ff9800',
                'timeout': '#ff5722',
                'database_error': '#673ab7',
                'api_error': '#2196f3',
                'xml_error': '#009688'
            }[cat] || '#757575';
            
            html += `<span style="display: inline-block; margin: 2px; padding: 2px 6px; background: ${catColor}; color: white; border-radius: 12px; font-size: 0.85em;">${formatErrorCategory(cat)}</span>`;
        });
        html += '</div>';
    }
    
    // Información sobre archivos y líneas de error
    if (errorData.file) {
        html += `<div style="margin-top: 5px;">Archivo: ${errorData.file} (línea ${errorData.line || 'desconocida'})</div>`;
    }
    
    // Si es un diagnóstico completo, mostrar secciones especiales
    if (errorData.exception_info) {
        html += formatExceptionInfo(errorData.exception_info);
    }
    
    // Información sobre el contexto
    if (errorData.context_info || errorData.context) {
        const contextInfo = errorData.context_info || errorData.context;
        html += '<div style="margin-top: 10px;">';
        html += '<details>';
        html += '<summary style="cursor: pointer; color: #1976d2;">Información de contexto</summary>';
        html += `<pre style="margin: 5px 0; padding: 5px; background: rgba(0,0,0,0.03); max-height: 150px; overflow: auto;">${JSON.stringify(contextInfo, null, 2)}</pre>`;
        html += '</details>';
        html += '</div>';
    }
    
    // Información detallada sobre diagnóstico
    if (errorData.diagnostico) {
        html += '<div style="margin-top: 10px;">';
        html += '<details>';
        html += '<summary style="cursor: pointer; color: #1976d2;">Diagnóstico detallado</summary>';
        html += `<pre style="margin: 5px 0; padding: 5px; background: rgba(0,0,0,0.03); max-height: 150px; overflow: auto;">${JSON.stringify(errorData.diagnostico, null, 2)}</pre>`;
        html += '</details>';
        html += '</div>';
    }
    
    // Si hay traza, mostrarla en modo colapsable
    if (errorData.trace) {
        html += '<div style="margin-top: 10px;">';
        html += '<details>';
        html += '<summary style="cursor: pointer; color: #1976d2;">Traza de error</summary>';
        html += '<pre style="margin: 5px 0; padding: 5px; background: rgba(0,0,0,0.03); max-height: 150px; overflow: auto;">';
        if (Array.isArray(errorData.trace)) {
            html += errorData.trace.join('\n');
        } else {
            html += errorData.trace;
        }
        html += '</pre>';
        html += '</details>';
        html += '</div>';
    }
    
    // Resto de información en formato JSON colapsable
    const otherKeys = Object.keys(errorData).filter(k => 
        !['message', 'code', 'file', 'line', 'error_categorias', 'exception_info', 
          'context_info', 'context', 'diagnostico', 'trace'].includes(k));
    
    if (otherKeys.length > 0) {
        html += '<div style="margin-top: 10px;">';
        html += '<details>';
        html += '<summary style="cursor: pointer; color: #1976d2;">Información adicional</summary>';
        html += '<pre style="margin: 5px 0; padding: 5px; background: rgba(0,0,0,0.03); max-height: 150px; overflow: auto;">';
        const additionalInfo = {};
        otherKeys.forEach(key => {
            additionalInfo[key] = errorData[key];
        });
        html += JSON.stringify(additionalInfo, null, 2);
        html += '</pre>';
        html += '</details>';
        html += '</div>';
    }
    
    html += '</div>';
    return html;
}

// Formatear categorías de error para mejor legibilidad
function formatErrorCategory(category) {
    const categoryLabels = {
        'file_not_found': 'Archivo no encontrado',
        'permission_denied': 'Permiso denegado',
        'memory_limit': 'Límite de memoria',
        'timeout': 'Tiempo de espera agotado',
        'database_error': 'Error de base de datos',
        'api_error': 'Error de API',
        'xml_error': 'Error de XML'
    };
    
    return categoryLabels[category] || category;
}

// Formatear información de excepción
function formatExceptionInfo(exceptionInfo) {
    let html = '<div style="margin-top: 8px; padding: 8px; background: rgba(0,0,0,0.02); border-left: 3px solid #f44336;">';
    html += `<div style="font-weight: bold;">Excepción: ${exceptionInfo.class || 'Desconocida'}</div>`;
    if (exceptionInfo.file) {
        html += `<div>Archivo: ${exceptionInfo.file} (línea ${exceptionInfo.line || 'desconocida'})</div>`;
    }
    html += '</div>';
    return html;
}
