// DTE Form Cleaner - Limpia el formulario DTE de manera inteligente
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando script de limpieza DTE');
    
    // Intentar varias veces para asegurar que el botón se encuentre
    function initLimpiarButton() {
        const limpiarFormBtn = document.getElementById('limpiarFormBtn');
        
        if (limpiarFormBtn) {
            console.log('Botón limpiar encontrado, asignando evento');
            
            // Eliminar cualquier evento previo
            limpiarFormBtn.removeEventListener('click', handleLimpiarClick);
            
            // Agregar el nuevo evento
            limpiarFormBtn.addEventListener('click', handleLimpiarClick);
        } else {
            console.log('Botón limpiar no encontrado, reintentando...');
            // Reintentar después de un momento
            setTimeout(initLimpiarButton, 100);
        }
    }
    
    function handleLimpiarClick(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('Botón limpiar clickeado');
        
        // Ejecutar limpieza directamente sin confirmación
        console.log('Ejecutando limpieza directamente...');
        limpiarFormularioDTE();
    }
    
    initLimpiarButton();
});

// Función de prueba para llamar desde la consola
window.testLimpiarDTE = function() {
    console.log('Función de prueba ejecutada');
    limpiarFormularioDTE();
};

// Función de prueba simple para verificar que puede limpiar campos básicos
window.testLimpiarSimple = function() {
    console.log('Prueba simple - limpiando algunos campos básicos...');
    
    // Probar limpiar algunos campos directamente
    const tipoDTE = document.getElementById('tipoDTE');
    if (tipoDTE) {
        console.log('tipoDTE encontrado, valor actual:', tipoDTE.value);
        tipoDTE.value = '39';
        console.log('tipoDTE cambiado a:', tipoDTE.value);
    }
    
    const folioDTE = document.getElementById('folioDTE');
    if (folioDTE) {
        console.log('folioDTE encontrado, valor actual:', folioDTE.value);
        folioDTE.value = '';
        console.log('folioDTE limpiado');
    }
    
    const rutReceptor = document.getElementById('rutReceptor');
    if (rutReceptor) {
        console.log('rutReceptor encontrado, valor actual:', rutReceptor.value);
        rutReceptor.value = '';
        console.log('rutReceptor limpiado');
    }
    
    console.log('Prueba simple completada');
};

// Exponer la función principal globalmente para debugging
window.limpiarFormularioDTE = limpiarFormularioDTE;

function limpiarFormularioDTE() {
    console.log('Iniciando limpieza del formulario DTE...');
    
    try {
        // 1. Limpiar campos de identificación DTE
        console.log('Limpiando sección identificación...');
        limpiarSeccionIdentificacion();
        
        // 2. Limpiar campos del receptor
        console.log('Limpiando sección receptor...');
        limpiarSeccionReceptor();
        
        // 3. Limpiar referencias (Nota de Crédito)
        console.log('Limpiando sección referencias...');
        limpiarSeccionReferencias();
        
        // 4. Limpiar items/productos
        console.log('Limpiando sección items...');
        limpiarSeccionItems();
        
        // 5. Limpiar totales
        console.log('Limpiando sección totales...');
        limpiarSeccionTotales();
        
        // 6. Limpiar descuentos y recargos
        console.log('Limpiando sección descuentos y recargos...');
        limpiarSeccionDescuentosRecargos();
        
        // 7. Resetear visibilidad de campos según tipo por defecto
        console.log('Reseteando visibilidad de campos...');
        resetearVisibilidadCampos();
        
        // 8. Limpiar contenedores de resultados y respuestas
        console.log('Limpiando resultados y respuestas...');
        limpiarResultadosYRespuestas();
        
        // 9. Mostrar notificación de éxito
        mostrarNotificacion('Formulario DTE limpiado exitosamente', 'success');
        
        console.log('Formulario DTE limpiado completamente');
        
    } catch (error) {
        console.error('Error al limpiar el formulario DTE:', error);
        mostrarNotificacion('Error al limpiar el formulario', 'error');
    }
}

function limpiarSeccionIdentificacion() {
    console.log('Iniciando limpieza de sección identificación...');
    
    // Resetear tipo DTE a Boleta (valor por defecto)
    const tipoDTE = document.getElementById('tipoDTE');
    if (tipoDTE) {
        console.log('Limpiando tipoDTE, valor actual:', tipoDTE.value);
        tipoDTE.value = '39';
        console.log('tipoDTE cambiado a:', tipoDTE.value);
    } else {
        console.warn('Elemento tipoDTE no encontrado');
    }
    
    // Limpiar folio y solicitar uno nuevo si existe la función
    const folioDTE = document.getElementById('folioDTE');
    if (folioDTE) {
        console.log('Limpiando folioDTE, valor actual:', folioDTE.value);
        folioDTE.value = '';
        console.log('folioDTE limpiado');
        // Intentar obtener un nuevo folio si la función existe
        if (typeof fetchFolio === 'function') {
            console.log('Solicitando nuevo folio...');
            setTimeout(() => {
                try {
                    fetchFolio();
                } catch (error) {
                    console.error('Error al obtener folio:', error);
                    // Si falla, poner un valor por defecto
                    folioDTE.value = '1';
                }
            }, 200); // Aumentar el timeout para evitar conflictos
        }
    } else {
        console.warn('Elemento folioDTE no encontrado');
    }
    
    // Resetear fecha emisión a hoy
    const fechaEmision = document.getElementById('fechaEmision');
    if (fechaEmision) {
        const today = new Date().toISOString().split('T')[0];
        fechaEmision.value = today;
    }
    
    // Limpiar fecha vencimiento
    const fechaVencimiento = document.getElementById('fechaVencimiento');
    if (fechaVencimiento) {
        fechaVencimiento.value = '';
    }
    
    // Resetear forma de pago
    const formaPago = document.getElementById('formaPago');
    if (formaPago) {
        formaPago.value = '1';
    }
    
    // Resetear indicador servicio
    const indicadorServicio = document.getElementById('indicadorServicio');
    if (indicadorServicio) {
        indicadorServicio.value = '3';
    }
}

function limpiarSeccionReceptor() {
    const camposReceptor = [
        'rutReceptor',
        'razonSocialReceptor', 
        'direccionReceptor',
        'comunaReceptor',
        'giroReceptor',
        'contactoReceptor'
    ];
    
    camposReceptor.forEach(campoId => {
        const campo = document.getElementById(campoId);
        if (campo) {
            campo.value = '';
            // Habilitar el campo en caso de que esté deshabilitado
            campo.readOnly = false;
            campo.disabled = false;
            campo.classList.remove('readonly-field');
            campo.style.backgroundColor = '';
            campo.style.cursor = '';
            campo.style.pointerEvents = '';
        }
    });
    
    // Resetear checkbox de receptor por defecto
    const defaultReceptorCheckbox = document.getElementById('defaultReceptorCheckbox');
    if (defaultReceptorCheckbox) {
        defaultReceptorCheckbox.checked = true; // Para boleta por defecto
    }
    
    // Limpiar mensajes de error/búsqueda
    const rutError = document.getElementById('rutError');
    if (rutError) {
        rutError.style.display = 'none';
        rutError.textContent = '';
    }
    
    const receptorSearchMessage = document.getElementById('receptorSearchMessage');
    if (receptorSearchMessage) {
        receptorSearchMessage.style.display = 'none';
        receptorSearchMessage.textContent = '';
    }
}

function limpiarSeccionReferencias() {
    const camposReferencia = [
        'folioRef',
        'fechaDocRef',
        'tipoDocRef',
        'codigoRef',
        'razonRef'
    ];
    
    camposReferencia.forEach(campoId => {
        const campo = document.getElementById(campoId);
        if (campo) {
            campo.value = '';
        }
    });
    
    // Limpiar mensaje de folio de referencia
    const folioRefMessage = document.getElementById('folioRefMessage');
    if (folioRefMessage) {
        folioRefMessage.style.display = 'none';
        folioRefMessage.textContent = '';
    }
}

function limpiarSeccionItems() {
    const itemsContainer = document.getElementById('itemsContainer');
    if (!itemsContainer) return;
    
    // Obtener todos los items existentes
    const itemRows = itemsContainer.querySelectorAll('.item-row');
    
    // Eliminar todos los items excepto el primero
    for (let i = itemRows.length - 1; i > 0; i--) {
        itemRows[i].remove();
    }
    
    // Limpiar el primer item (item_0)
    const camposPrimerItem = [
        'nombre_0',
        'cantidad_0',
        'descripcion_0',
        'unidad_0',
        'precio_0',
        'precio_total_0',
        'descuento_0',
        'recargo_0',
        'montoItem_0'
    ];
    
    camposPrimerItem.forEach(campoId => {
        const campo = document.getElementById(campoId);
        if (campo) {
            if (campoId === 'cantidad_0') {
                campo.value = '1';
            } else if (campoId === 'unidad_0') {
                campo.value = 'un';
            } else if (campoId.includes('precio') || campoId.includes('descuento') || campoId.includes('recargo') || campoId.includes('monto')) {
                campo.value = '0';
            } else {
                campo.value = '';
            }
        }
    });
}

function limpiarSeccionTotales() {
    const camposTotales = [
        'montoNeto',
        'tasaIVA',
        'ivaCalculado',
        'montoTotal'
    ];
    
    camposTotales.forEach(campoId => {
        const campo = document.getElementById(campoId);
        if (campo) {
            if (campoId === 'tasaIVA') {
                campo.value = '19';
            } else {
                campo.value = '0';
            }
        }
    });
}

function limpiarSeccionDescuentosRecargos() {
    const camposDescuentosRecargos = [
        'tipoMov',
        'descTipoValor',
        'descValor',
        'descDescripcion'
    ];
    
    camposDescuentosRecargos.forEach(campoId => {
        const campo = document.getElementById(campoId);
        if (campo) {
            campo.value = '';
        }
    });
    
    // Deshabilitar la sección de descuentos y recargos
    const enableDescuentosCheckbox = document.getElementById('enableDescuentosCheckbox');
    if (enableDescuentosCheckbox) {
        enableDescuentosCheckbox.checked = false;
        // Trigger el evento para ocultar la sección
        if (typeof toggleDescuentosRecargos === 'function') {
            toggleDescuentosRecargos();
        }
    }
}

function resetearVisibilidadCampos() {
    // Trigger las funciones de toggle para resetear la visibilidad según el tipo DTE
    if (typeof toggleDTEFields === 'function') {
        console.log('Llamando a toggleDTEFields...');
        toggleDTEFields();
    }
    
    // En lugar de handleReceptorFields, usar toggleDTEFields que maneja todo
    // handleReceptorFields parece tener problemas con aplicarReceptorGenerico
    console.log('Aplicando receptor genérico directamente...');
    
    // Aplicar receptor genérico para boletas directamente
    const tipoDTE = document.getElementById('tipoDTE');
    if (tipoDTE && tipoDTE.value === '39') {
        const defaultReceptorCheckbox = document.getElementById('defaultReceptorCheckbox');
        if (defaultReceptorCheckbox) {
            defaultReceptorCheckbox.checked = true;
            // Llamar a toggleDefaultReceptor si existe
            if (typeof toggleDefaultReceptor === 'function') {
                toggleDefaultReceptor();
            }
        }
    }
    
    // Ocultar sección de referencias (solo para Nota de Crédito)
    const referenciasSection = document.getElementById('referenciasSection');
    if (referenciasSection) {
        referenciasSection.style.display = 'none';
    }
    
    // Resetear inventario checkbox a activado
    const enableInventarioCheckbox = document.getElementById('enableInventarioCheckbox');
    if (enableInventarioCheckbox) {
        enableInventarioCheckbox.checked = true;
    }
}

function limpiarResultadosYRespuestas() {
    // Limpiar resultado JSON
    const jsonResult = document.getElementById('jsonResult');
    if (jsonResult) {
        jsonResult.innerHTML = '';
    }
    
    // Ocultar acciones JSON
    const jsonActions = document.querySelector('.json-actions');
    if (jsonActions) {
        jsonActions.style.display = 'none';
    }
    
    // Limpiar y ocultar contenedor de envío DTE
    const enviarDTEContainer = document.getElementById('enviarDTEContainer');
    if (enviarDTEContainer) {
        enviarDTEContainer.style.display = 'none';
    }
    
    // Limpiar contenedor de respuesta API
    const responseContainer = document.getElementById('responseContainer');
    if (responseContainer) {
        responseContainer.style.display = 'none';
        const apiResponse = document.getElementById('apiResponse');
        if (apiResponse) {
            apiResponse.innerHTML = '';
        }
    }
    
    // Ocultar indicador de carga
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
    }
    
    // Ocultar botón de imprimir PDF
    const printPdfBtn = document.getElementById('printPdfBtn');
    if (printPdfBtn) {
        printPdfBtn.style.display = 'none';
    }
    
    // Limpiar mensaje de copia
    const copyMessage = document.getElementById('copyMessage');
    if (copyMessage) {
        copyMessage.style.display = 'none';
    }
}

function mostrarNotificacion(mensaje, tipo = 'success') {
    // Crear elemento de notificación
    const notificacion = document.createElement('div');
    notificacion.className = `dte-notification ${tipo}`;
    
    const icono = tipo === 'success' ? 'check-circle' : 'exclamation-triangle';
    const color = tipo === 'success' ? '#28a745' : '#dc3545';
    
    notificacion.innerHTML = `<i class="fas fa-${icono}"></i> ${mensaje}`;
    notificacion.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${color};
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 3000;
        display: flex;
        align-items: center;
        gap: 10px;
        animation: slideInTop 0.3s ease-out;
        max-width: 300px;
        font-size: 14px;
    `;
    
    // Agregar estilos de animación si no existen
    if (!document.getElementById('dte-cleaner-styles')) {
        const style = document.createElement('style');
        style.id = 'dte-cleaner-styles';
        style.textContent = `
            @keyframes slideInTop {
                from {
                    transform: translateY(-100%);
                    opacity: 0;
                }
                to {
                    transform: translateY(0);
                    opacity: 1;
                }
            }
            
            @keyframes slideOutTop {
                from {
                    transform: translateY(0);
                    opacity: 1;
                }
                to {
                    transform: translateY(-100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(notificacion);
    
    // Remover después de 4 segundos
    setTimeout(() => {
        notificacion.style.animation = 'slideOutTop 0.3s ease-in';
        setTimeout(() => {
            if (notificacion.parentNode) {
                notificacion.parentNode.removeChild(notificacion);
            }
        }, 300);
    }, 4000);
}

// Función auxiliar para limpiar específicamente según el tipo de DTE
function limpiarSegunTipoDTE(tipoDTE) {
    switch(tipoDTE) {
        case '33': // Factura
        case '34': // Factura Exenta
            // Para facturas, limpiar campos específicos de factura
            break;
        case '39': // Boleta
            // Para boletas, aplicar receptor genérico
            if (typeof handleReceptorFields === 'function') {
                handleReceptorFields();
            }
            break;
        case '61': // Nota de Crédito
            // Para notas de crédito, mostrar sección de referencias
            const referenciasSection = document.getElementById('referenciasSection');
            if (referenciasSection) {
                referenciasSection.style.display = 'block';
            }
            break;
    }
}

// Función para mostrar confirmación personalizada
function mostrarConfirmacionPersonalizada(titulo, mensaje, onConfirm, onCancel) {
    // Crear el overlay
    const overlay = document.createElement('div');
    overlay.className = 'confirm-overlay';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 10000;
        display: flex;
        justify-content: center;
        align-items: center;
    `;
    
    // Crear el modal
    const modal = document.createElement('div');
    modal.className = 'confirm-modal';
    modal.style.cssText = `
        background: white;
        border-radius: 8px;
        padding: 30px;
        max-width: 400px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        text-align: center;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;
    
    modal.innerHTML = `
        <div style="margin-bottom: 20px;">
            <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: #f39c12; margin-bottom: 15px;"></i>
            <h3 style="margin: 0 0 10px 0; color: #2c3e50; font-size: 18px;">${titulo}</h3>
            <p style="margin: 0; color: #7f8c8d; font-size: 14px;">${mensaje}</p>
        </div>
        <div style="display: flex; gap: 10px; justify-content: center;">
            <button id="confirm-cancel" style="
                padding: 10px 20px;
                border: 1px solid #bdc3c7;
                background: white;
                color: #7f8c8d;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: all 0.3s;
            ">Cancelar</button>
            <button id="confirm-ok" style="
                padding: 10px 20px;
                border: none;
                background: #e74c3c;
                color: white;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: all 0.3s;
            ">Sí, limpiar</button>
        </div>
    `;
    
    overlay.appendChild(modal);
    document.body.appendChild(overlay);
    
    // Agregar eventos a los botones
    const cancelBtn = modal.querySelector('#confirm-cancel');
    const okBtn = modal.querySelector('#confirm-ok');
    
    function closeModal() {
        document.body.removeChild(overlay);
    }
    
    cancelBtn.addEventListener('click', () => {
        closeModal();
        if (onCancel) onCancel();
    });
    
    okBtn.addEventListener('click', () => {
        closeModal();
        if (onConfirm) onConfirm();
    });
    
    // Cerrar con ESC
    function handleKeyPress(e) {
        if (e.key === 'Escape') {
            closeModal();
            if (onCancel) onCancel();
            document.removeEventListener('keydown', handleKeyPress);
        }
    }
    
    document.addEventListener('keydown', handleKeyPress);
    
    // Cerrar al hacer click en el overlay (fuera del modal)
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            closeModal();
            if (onCancel) onCancel();
        }
    });
    
    // Agregar estilos hover dinámicamente
    cancelBtn.addEventListener('mouseenter', () => {
        cancelBtn.style.background = '#ecf0f1';
        cancelBtn.style.borderColor = '#95a5a6';
    });
    
    cancelBtn.addEventListener('mouseleave', () => {
        cancelBtn.style.background = 'white';
        cancelBtn.style.borderColor = '#bdc3c7';
    });
    
    okBtn.addEventListener('mouseenter', () => {
        okBtn.style.background = '#c0392b';
    });
    
    okBtn.addEventListener('mouseleave', () => {
        okBtn.style.background = '#e74c3c';
    });
    
    // Enfocar el botón de cancelar por defecto
    setTimeout(() => cancelBtn.focus(), 100);
}