/**
 * Funcionalidad para simular una venta que solo afecta al inventario
 * sin generar un DTE (Documento Tributario Electrónico)
 */

// Función para mostrar notificaciones
function mostrarNotificacion(mensaje, tipo = 'info') {
    // Crear elemento de notificación
    const notificacion = document.createElement('div');
    notificacion.className = `notificacion ${tipo}`;
    notificacion.innerHTML = `
        <div class="notificacion-contenido">
            <i class="fas ${tipo === 'success' ? 'fa-check-circle' : 
                           tipo === 'error' ? 'fa-exclamation-circle' : 
                           'fa-info-circle'}"></i>
            <span>${mensaje}</span>
        </div>
    `;
    
    // Estilos para la notificación
    notificacion.style.position = 'fixed';
    notificacion.style.top = '20px';
    notificacion.style.right = '20px';
    notificacion.style.zIndex = '9999';
    notificacion.style.backgroundColor = tipo === 'success' ? '#4CAF50' : 
                                        tipo === 'error' ? '#F44336' : 
                                        '#2196F3';
    notificacion.style.color = 'white';
    notificacion.style.padding = '15px';
    notificacion.style.borderRadius = '5px';
    notificacion.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
    notificacion.style.display = 'flex';
    notificacion.style.alignItems = 'center';
    notificacion.style.maxWidth = '300px';
    
    // Agregar al DOM
    document.body.appendChild(notificacion);
    
    // Eliminar después de 5 segundos
    setTimeout(() => {
        notificacion.style.opacity = '0';
        notificacion.style.transition = 'opacity 0.5s';
        setTimeout(() => {
            document.body.removeChild(notificacion);
        }, 500);
    }, 5000);
}

// Función para recopilar los productos del formulario DTE
function obtenerProductosFormulario() {
    const productos = [];
    const filas = document.querySelectorAll('.item-row');

    filas.forEach((fila, index) => {
        // Obtener valores de los campos
        const nombre = fila.querySelector(`.item-nombre`).value;
        const cantidad = parseInt(fila.querySelector(`.item-cantidad`).value) || 0;
        const precio = parseFloat(fila.querySelector(`.item-precio`).value) || 0;

        // Obtener el ID del repuesto usando la misma lógica que dte_productos.js
        const dataRepuestoId = fila.getAttribute('data-repuesto-id');
        const inputField = fila.querySelector('.repuesto-id');
        const inputRepuestoId = inputField ? inputField.value : null;

        // Usar el ID del atributo data o del campo oculto
        const repuestoId = dataRepuestoId || inputRepuestoId;

        console.log('Debug simulación - Fila ' + (index + 1) + ':', {
            nombre: nombre,
            cantidad: cantidad,
            precio: precio,
            dataRepuestoId: dataRepuestoId,
            inputRepuestoId: inputRepuestoId,
            repuestoIdFinal: repuestoId
        });

        // Solo agregar si tiene cantidad y nombre
        if (cantidad > 0 && nombre.trim() !== '') {
            productos.push({
                repuesto_id: repuestoId,
                nombre: nombre,
                cantidad: cantidad,
                precio: precio
            });
        }
    });

    console.log('Productos recopilados para simulación:', productos);
    return productos;
}

// Función principal para simular la venta
async function simularVenta() {
    try {
        // Obtener productos del formulario
        const productos = obtenerProductosFormulario();
        
        // Filtrar solo los productos que tienen ID de repuesto
        const productosConId = productos.filter(p => p.repuesto_id);
        
        // Verificar que haya productos con ID
        if (productosConId.length === 0) {
            mostrarNotificacion('No hay productos con ID de repuesto para actualizar el inventario', 'error');
            return;
        }
        
        // Confirmar la acción
        if (!confirm(`¿Está seguro de simular esta venta? 
        Se actualizará el inventario para ${productosConId.length} productos sin generar DTE.
        Esta acción no se puede deshacer.`)) {
            return;
        }
        
        // Obtener el ID del almacén seleccionado
        const almacenSelect = document.getElementById('almacen_id');
        const almacenId = almacenSelect ? almacenSelect.value : 1; // Valor por defecto
        
        // Preparar los datos para enviar al servidor
        const data = {
            simulacion: true,
            productos: productosConId,
            usuario_id: window.usuarioId || 1,
            almacen_id: almacenId,
            referencia: 'Simulación de venta - ' + new Date().toLocaleString()
        };
        
        // Mostrar indicador de carga
        mostrarNotificacion('Procesando simulación de venta...', 'info');
        
        // Enviar los datos al servidor
        const response = await fetch('registrar_salida_inventario.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache'
            },
            body: JSON.stringify(data)
        });
        
        // Verificar si la respuesta es exitosa
        if (!response.ok) {
            throw new Error(`Error en la respuesta del servidor: ${response.status} ${response.statusText}`);
        }
        
        // Procesar la respuesta
        const result = await response.json();
        
        if (result.status === 'success') {
            mostrarNotificacion(`Simulación completada: ${result.message}`, 'success');
            
            // Opcional: Limpiar el formulario o realizar otras acciones
            if (confirm('¿Desea limpiar el formulario?')) {
                document.getElementById('dteForm').reset();
                // Reiniciar los items a uno solo
                const itemsContainer = document.getElementById('itemsContainer');
                const primerItem = itemsContainer.querySelector('.item-row');
                
                // Eliminar todos los items excepto el primero
                while (itemsContainer.children.length > 1) {
                    itemsContainer.removeChild(itemsContainer.lastChild);
                }
                
                // Limpiar los valores del primer item
                if (primerItem) {
                    primerItem.querySelector('.item-nombre').value = '';
                    primerItem.querySelector('.item-cantidad').value = '1';
                    primerItem.querySelector('.item-precio').value = '0';
                    
                    // Si existe el campo de repuesto ID, limpiarlo
                    const repuestoIdInput = primerItem.querySelector('.repuesto-id');
                    if (repuestoIdInput) {
                        repuestoIdInput.value = '';
                    }
                }
                
                // Actualizar totales
                if (typeof actualizarMontoNeto === 'function') {
                    actualizarMontoNeto();
                }
            }
        } else {
            throw new Error(result.message || 'Error desconocido al simular la venta');
        }
    } catch (error) {
        console.error('Error al simular la venta:', error);
        mostrarNotificacion(`Error: ${error.message}`, 'error');
    }
}

// Inicializar cuando el DOM esté cargado
document.addEventListener('DOMContentLoaded', function() {
    // Buscar el botón de simulación
    const simularVentaBtn = document.getElementById('simularVentaBtn');
    
    if (simularVentaBtn) {
        console.log('Botón de simulación de venta encontrado, asignando evento');
        
        // Asignar el evento de clic
        simularVentaBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Botón de simulación de venta clickeado');
            simularVenta();
        });
    } else {
        console.error('No se encontró el botón de simulación de venta');
    }
    
    // Agregar estilos para las notificaciones
    const style = document.createElement('style');
    style.textContent = `
        .notificacion {
            transition: opacity 0.5s;
            opacity: 1;
        }
        .notificacion-contenido {
            display: flex;
            align-items: center;
        }
        .notificacion-contenido i {
            margin-right: 10px;
            font-size: 1.2em;
        }
    `;
    document.head.appendChild(style);
});