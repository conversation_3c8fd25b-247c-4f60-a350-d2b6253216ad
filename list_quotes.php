<?php
session_start();
header('Content-Type: application/json; charset=utf-8');

date_default_timezone_set('America/Santiago');

if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => 'No autorizado']);
    exit;
}

require_once 'db_connection.php';

try {
    $conn = getConnection();

    $stmt = $conn->query("SELECT id, numero, DATE_FORMAT(fecha, '%d-%m-%Y %H:%i') AS fecha, cliente_nombre, total FROM tb_cotizaciones ORDER BY fecha DESC");
    $quotes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode(['status' => 'success', 'quotes' => $quotes], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => 'Error al obtener cotizaciones', 'error_details' => $e->getMessage()]);
}
?>
