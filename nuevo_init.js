/**
 * Script de inicialización para solucionar el problema del botón de envío directo
 */
console.log("Inicializando script de corrección para botón de generación y envío directo");

// Esperar a que el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM cargado en nuevo_init.js");
    
    // Buscar el botón por su ID
    const generateAndSendBtn = document.getElementById('generateAndSendBtn');
    if (generateAndSendBtn) {
        console.log("Botón generateAndSendBtn encontrado, asignando nuevo event listener");
        
        // Eliminar cualquier event listener previo (si es posible)
        if (typeof generateAndSendBtn.replaceWith === 'function') {
            const newBtn = generateAndSendBtn.cloneNode(true);
            generateAndSendBtn.replaceWith(newBtn);
            
            // Asignar el evento al botón clonado
            newBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log("Clic en botón - nuevo_init.js");
                if (typeof generateAndSendDirect === 'function') {
                    generateAndSendDirect();
                } else {
                    alert("Error: La función generateAndSendDirect no está definida");
                    console.error("La función generateAndSendDirect no está definida");
                }
            });
        } else {
            // Asignar directamente si el reemplazo no es posible
            generateAndSendBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log("Clic en botón - nuevo_init.js");
                if (typeof generateAndSendDirect === 'function') {
                    generateAndSendDirect();
                } else {
                    alert("Error: La función generateAndSendDirect no está definida");
                    console.error("La función generateAndSendDirect no está definida");
                }
            });
        }
        
        // Prueba de interactividad
        generateAndSendBtn.style.position = 'relative';
        generateAndSendBtn.style.overflow = 'hidden';
        
        // Agregar efecto de ondas al hacer clic para confirmar que el botón es interactivo
        generateAndSendBtn.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            ripple.classList.add('ripple-effect');
            ripple.style.position = 'absolute';
            ripple.style.borderRadius = '50%';
            ripple.style.backgroundColor = 'rgba(255, 255, 255, 0.4)';
            ripple.style.width = '100px';
            ripple.style.height = '100px';
            ripple.style.transform = 'translate(-50%, -50%)';
            ripple.style.animation = 'ripple-animation 0.6s linear';
            
            // Posicionar el efecto donde ocurrió el clic
            ripple.style.left = (e.clientX - this.getBoundingClientRect().left) + 'px';
            ripple.style.top = (e.clientY - this.getBoundingClientRect().top) + 'px';
            
            this.appendChild(ripple);
            
            // Limpiar después de la animación
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
        
        // Agregar estilo para la animación
        const style = document.createElement('style');
        style.textContent = `
        @keyframes ripple-animation {
            from {
                opacity: 1;
                transform: scale(0);
            }
            to {
                opacity: 0;
                transform: scale(4);
            }
        }
        `;
        document.head.appendChild(style);
        
        // Añadir botón de emergencia para testear
        const emergencyBtn = document.createElement('button');
        emergencyBtn.textContent = 'BOTÓN DE EMERGENCIA';
        emergencyBtn.style.position = 'fixed';
        emergencyBtn.style.bottom = '10px';
        emergencyBtn.style.right = '10px';
        emergencyBtn.style.zIndex = '9999';
        emergencyBtn.style.backgroundColor = 'red';
        emergencyBtn.style.color = 'white';
        emergencyBtn.style.padding = '10px';
        emergencyBtn.style.border = 'none';
        emergencyBtn.style.borderRadius = '5px';
        emergencyBtn.style.cursor = 'pointer';
        
        emergencyBtn.addEventListener('click', function() {
            console.log('Botón de emergencia clickeado');
            alert('Llamando a generateAndSendDirect()');
            if (typeof generateAndSendDirect === 'function') {
                generateAndSendDirect();
            } else {
                alert('ERROR: La función generateAndSendDirect no está definida');
            }
        });
        
        document.body.appendChild(emergencyBtn);
        
    } else {
        console.error("Error: No se encontró el botón con ID 'generateAndSendBtn'");
        
        // Si no se encuentra el botón, intentar agregar uno nuevo
        const dteForm = document.getElementById('dteForm');
        if (dteForm) {
            console.log("Formulario DTE encontrado, agregando botón nuevo");
            const newBtn = document.createElement('button');
            newBtn.id = 'generateAndSendBtn';
            newBtn.type = 'button';
            newBtn.className = 'json-btn';
            newBtn.style.marginLeft = '10px';
            newBtn.style.backgroundColor = '#28a745';
            newBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Generar y Enviar DTE (Botón Nuevo)';
            
            newBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log("Botón nuevo clickeado");
                if (typeof generateAndSendDirect === 'function') {
                    generateAndSendDirect();
                } else {
                    alert("Error: La función generateAndSendDirect no está definida");
                }
            });
            
            // Añadir al final del formulario
            dteForm.appendChild(newBtn);
        }
    }
    
    console.log("Inicialización de nuevo_init.js completada");
});
