<?php
require_once 'db_connection.php';
header('Content-Type: application/json');

try {
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    if (!$data) {
        throw new Exception('Datos de venta inválidos');
    }

    $conn = getConnection();
    $conn->beginTransaction();

    // Preparar los valores, manejando NULL para fecha_compromiso
    $fecha_compromiso = !empty($data['fecha_compromiso']) ? $data['fecha_compromiso'] : null;

    // Insertar la venta principal
    $sql = "INSERT INTO venta (
        tipo_documento, 
        numero_documento,
        condiciones_pago,
        total,
        cliente_id,
        fecha_compromiso,
        fecha_emision,
        estado,
        abono,
        created_at,
        updated_at
    ) VALUES (
        :tipo_documento,
        :numero_documento,
        :condiciones_pago,
        :total,
        " . ($data['cliente_id'] ? ":cliente_id" : "NULL") . ",
        " . ($fecha_compromiso ? ":fecha_compromiso" : "NULL") . ",
        NOW(),
        CASE 
            WHEN " . ($fecha_compromiso ? "1" : "0") . "
            THEN 'PENDIENTE' 
            ELSE 'COMPLETADA' 
        END,
        :abono,
        NOW(),
        NOW()
    )";

    $stmt = $conn->prepare($sql);
    
    $params = [
        ':tipo_documento' => $data['tipo_documento'],
        ':numero_documento' => $data['numero_documento'],
        ':condiciones_pago' => $data['condiciones_pago'],
        ':total' => $data['total'],
        ':abono' => $data['abono']
    ];
    
    // Solo agregar cliente_id al array de parámetros si existe
    if ($data['cliente_id']) {
        $params[':cliente_id'] = $data['cliente_id'];
    }

    // Solo agregar fecha_compromiso al array de parámetros si existe
    if ($fecha_compromiso) {
        $params[':fecha_compromiso'] = $fecha_compromiso;
    }
    
    $stmt->execute($params);

    $ventaId = $conn->lastInsertId();

    // Insertar los detalles de la venta en la tabla detalle_orden_compra
    $sqlDetalle = "INSERT INTO detalle_orden_compra (
        orden_compra_id,
        repuesto_id,
        cantidad,
        precio_unitario,
        subtotal,
        descuento,
        tipo_descuento
    ) VALUES (
        :venta_id,
        :repuesto_id,
        :cantidad,
        :precio_unitario,
        :subtotal,
        :descuento,
        :tipo_descuento
    )";

    $stmtDetalle = $conn->prepare($sqlDetalle);

    foreach ($data['detalles'] as $detalle) {
        $stmtDetalle->execute([
            ':venta_id' => $ventaId,
            ':repuesto_id' => $detalle['repuesto_id'],
            ':cantidad' => $detalle['cantidad'],
            ':precio_unitario' => $detalle['precio_unitario'],
            ':subtotal' => $detalle['subtotal'],
            ':descuento' => $detalle['descuento'],
            ':tipo_descuento' => $detalle['tipo_descuento']
        ]);
    }

    $conn->commit();

    echo json_encode([
        'status' => 'success',
        'message' => 'Venta procesada exitosamente',
        'venta_id' => $ventaId
    ]);

} catch (Exception $e) {
    if (isset($conn)) {
        $conn->rollBack();
    }

    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
