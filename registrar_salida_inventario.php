<?php
require_once 'db_connection.php';
header('Content-Type: application/json');

// Habilitar logs de error para depuración (solo en logs, no en pantalla)
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Función para log específico de simulación
function logSimulacion($mensaje) {
    $timestamp = date('Y-m-d H:i:s');
    error_log("[$timestamp] SIMULACION: $mensaje");
    file_put_contents('simulacion_debug.log', "[$timestamp] $mensaje\n", FILE_APPEND | LOCK_EX);
}

// Log de depuración
logSimulacion("Iniciando script registrar_salida_inventario.php");

// Verificar que la solicitud sea POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    logSimulacion("Método no permitido - " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => 'Método no permitido']);
    exit;
}

// Obtener los datos enviados
$input = file_get_contents('php://input');
logSimulacion("Input recibido - " . $input);

$data = json_decode($input, true);
logSimulacion("Datos decodificados - " . print_r($data, true));

// Verificar si hubo error en la decodificación JSON
if (json_last_error() !== JSON_ERROR_NONE) {
    logSimulacion("Error al decodificar JSON: " . json_last_error_msg());
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'Error en formato JSON: ' . json_last_error_msg()]);
    exit;
}

// Verificar si es una simulación o un proceso normal de DTE
$esSimulacion = isset($data['simulacion']) && $data['simulacion'] === true;
logSimulacion("Es simulación: " . ($esSimulacion ? 'SÍ' : 'NO'));

// Validar los datos según el tipo de operación
if ($esSimulacion) {
    logSimulacion("Procesando simulación de venta");

    // Para simulación, solo necesitamos productos
    if (!isset($data['productos']) || empty($data['productos'])) {
        logSimulacion("Error: No hay productos para simular la venta");
        http_response_code(400);
        echo json_encode(['status' => 'error', 'message' => 'No hay productos para simular la venta']);
        exit;
    }

    logSimulacion("Productos recibidos: " . count($data['productos']));

    // Usar la referencia proporcionada o crear una por defecto
    $referencia = isset($data['referencia']) ? $data['referencia'] : 'Simulación de venta';

    // Obtener el ID de usuario y almacén
    $usuarioId = isset($data['usuario_id']) ? $data['usuario_id'] : 1;
    $almacenId = isset($data['almacen_id']) ? $data['almacen_id'] : 1;

    logSimulacion("Referencia: $referencia, Usuario ID: $usuarioId, Almacén ID: $almacenId");
} else {
    // Para DTE, necesitamos el ID del DTE y productos
    if (!isset($data['dte_id']) || !isset($data['productos'])) {
        http_response_code(400);
        echo json_encode(['status' => 'error', 'message' => 'Datos incompletos']);
        exit;
    }
    
    // Si no hay productos con ID, devolver un mensaje informativo
    if (empty($data['productos'])) {
        echo json_encode([
            'status' => 'info', 
            'message' => 'No hay productos para registrar salida de inventario',
            'dte_id' => $data['dte_id']
        ]);
        exit;
    }
}

try {
    logSimulacion("Iniciando proceso de registro de salida de inventario");

    $conn = getConnection();
    logSimulacion("Conexión a base de datos establecida");

    // Iniciar transacción
    $conn->beginTransaction();
    logSimulacion("Transacción iniciada");
    
    // Si es un DTE, obtener información para la referencia
    if (!$esSimulacion) {
        $stmtDTE = $conn->prepare("SELECT tipo_dte, folio FROM tb_facturas_dte WHERE id = :dte_id");
        $stmtDTE->bindParam(':dte_id', $data['dte_id']);
        $stmtDTE->execute();
        $dteInfo = $stmtDTE->fetch(PDO::FETCH_ASSOC);
        
        if (!$dteInfo) {
            throw new Exception("No se encontró el DTE con ID: " . $data['dte_id']);
        }
        
        // Crear la referencia para el movimiento
        $tipoDocumento = '';
        switch ($dteInfo['tipo_dte']) {
            case '33': $tipoDocumento = 'Factura'; break;
            case '34': $tipoDocumento = 'Factura Exenta'; break;
            case '39': $tipoDocumento = 'Boleta'; break;
            case '61': $tipoDocumento = 'Nota de Crédito'; break;
            default: $tipoDocumento = 'DTE'; break;
        }
        $referencia = $tipoDocumento . ' #' . $dteInfo['folio'];
        
        // Obtener el ID de usuario y almacén
        $usuarioId = isset($data['usuario_id']) ? $data['usuario_id'] : 1;
        $almacenId = isset($data['almacen_id']) ? $data['almacen_id'] : 1;
    }
    
    // Preparar la consulta para insertar movimientos de inventario
    $stmt = $conn->prepare("INSERT INTO movimiento_inventario (
        repuesto_id,
        tipo_movimiento,
        cantidad,
        fecha_movimiento,
        usuario,
        referencia_documento,
        almacen_id
    ) VALUES (
        :repuesto_id,
        'SALIDA',
        :cantidad,
        NOW(),
        :usuario,
        :referencia,
        :almacen_id
    )");
    
    // Contadores para el reporte
    $productosRegistrados = 0;
    $productosOmitidos = 0;
    $errores = [];
    
    // Insertar cada movimiento de inventario
    logSimulacion("Iniciando procesamiento de productos");
    foreach ($data['productos'] as $index => $producto) {
        logSimulacion("Procesando producto $index: " . print_r($producto, true));

        // Verificar que el producto tenga un ID válido
        if (empty($producto['repuesto_id'])) {
            logSimulacion("Producto omitido: sin ID válido");
            $productosOmitidos++;
            continue; // Saltar este producto si no tiene ID
        }

        logSimulacion("Producto ID válido: " . $producto['repuesto_id']);

        // Verificar que la cantidad sea válida
        $cantidad = intval($producto['cantidad']);
        if ($cantidad <= 0) {
            logSimulacion("Producto omitido: cantidad inválida ($cantidad)");
            $productosOmitidos++;
            continue; // Saltar este producto si la cantidad no es válida
        }

        logSimulacion("Cantidad válida: $cantidad");
        
        // Obtener los lotes disponibles para este producto (FIFO)
        logSimulacion("Consultando lotes para repuesto_id: " . $producto['repuesto_id'] . ", almacen_id: $almacenId");

        $stmtLotes = $conn->prepare("
            SELECT lote, cantidad
            FROM stock
            WHERE repuesto_id = :repuesto_id
            AND almacen_id = :almacen_id
            AND cantidad > 0
            ORDER BY created_at ASC
        ");
        $stmtLotes->bindParam(':repuesto_id', $producto['repuesto_id']);
        $stmtLotes->bindParam(':almacen_id', $almacenId);
        $stmtLotes->execute();

        $lotes = $stmtLotes->fetchAll(PDO::FETCH_ASSOC);
        logSimulacion("Lotes encontrados: " . count($lotes) . " - " . print_r($lotes, true));

        // Verificar si hay stock suficiente
        $stockTotal = array_sum(array_column($lotes, 'cantidad'));
        logSimulacion("Stock total disponible: $stockTotal, cantidad requerida: $cantidad");
        if ($stockTotal < $cantidad) {
            $errores[] = [
                'repuesto_id' => $producto['repuesto_id'],
                'nombre' => $producto['nombre'] ?? 'Producto #' . $producto['repuesto_id'],
                'cantidad_solicitada' => $cantidad,
                'stock_disponible' => $stockTotal
            ];
            continue; // Saltar este producto si no hay stock suficiente
        }
        
        // Cantidad pendiente por procesar
        $cantidadPendiente = $cantidad;
        
        // Actualizar el stock de cada lote (FIFO)
        logSimulacion("Iniciando actualización de lotes");
        foreach ($lotes as $lote) {
            $cantidadLote = min($cantidadPendiente, intval($lote['cantidad']));
            logSimulacion("Procesando lote: " . $lote['lote'] . ", cantidad lote: $cantidadLote");

            if ($cantidadLote <= 0) continue;

            // Registrar el movimiento de salida para este lote
            logSimulacion("Registrando movimiento de inventario");
            $stmt->bindParam(':repuesto_id', $producto['repuesto_id']);
            $stmt->bindParam(':cantidad', $cantidadLote);
            $usuario = 'admin'; // Usuario fijo para simulaciones
            $stmt->bindParam(':usuario', $usuario);
            $stmt->bindParam(':referencia', $referencia);
            $stmt->bindParam(':almacen_id', $almacenId);

            try {
                $stmt->execute();
                logSimulacion("Movimiento de inventario registrado exitosamente");
            } catch (Exception $e) {
                logSimulacion("ERROR al registrar movimiento: " . $e->getMessage());
                throw $e;
            }

            // Actualizar el stock en la tabla stock
            logSimulacion("Actualizando stock en tabla stock");
            $stmtUpdateStock = $conn->prepare("
                UPDATE stock
                SET cantidad = cantidad - :cantidad
                WHERE repuesto_id = :repuesto_id
                AND almacen_id = :almacen_id
                AND lote = :lote
            ");
            $stmtUpdateStock->bindParam(':cantidad', $cantidadLote);
            $stmtUpdateStock->bindParam(':repuesto_id', $producto['repuesto_id']);
            $stmtUpdateStock->bindParam(':almacen_id', $almacenId);
            $stmtUpdateStock->bindParam(':lote', $lote['lote']);

            try {
                $stmtUpdateStock->execute();
                logSimulacion("Stock actualizado exitosamente");
            } catch (Exception $e) {
                logSimulacion("ERROR al actualizar stock: " . $e->getMessage());
                throw $e;
            }

            $cantidadPendiente -= $cantidadLote;
            logSimulacion("Cantidad pendiente restante: $cantidadPendiente");

            if ($cantidadPendiente <= 0) break;
        }

        logSimulacion("Producto procesado exitosamente");
        
        $productosRegistrados++;
    }
    
    // Confirmar la transacción
    $conn->commit();
    
    // Preparar mensaje según los resultados
    $mensaje = "Se registraron salidas de inventario para $productosRegistrados productos";
    
    if ($productosOmitidos > 0) {
        $mensaje .= ". Se omitieron $productosOmitidos productos (sin ID o cantidad inválida)";
    }
    
    if (!empty($errores)) {
        $mensaje .= ". Algunos productos no tenían stock suficiente";
    }
    
    // Preparar la respuesta según el tipo de operación
    $respuesta = [
        'status' => 'success', 
        'message' => $mensaje,
        'productos_registrados' => $productosRegistrados,
        'productos_omitidos' => $productosOmitidos,
        'errores_stock' => $errores
    ];
    
    // Agregar el ID del DTE si no es una simulación
    if (!$esSimulacion && isset($data['dte_id'])) {
        $respuesta['dte_id'] = $data['dte_id'];
    }
    
    echo json_encode($respuesta);
    
} catch(Exception $e) {
    logSimulacion("ERROR CAPTURADO: " . $e->getMessage());
    logSimulacion("Stack trace: " . $e->getTraceAsString());

    // Revertir la transacción en caso de error
    if (isset($conn)) {
        $conn->rollBack();
        logSimulacion("Transacción revertida");
    }

    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Error al registrar salidas de inventario: ' . $e->getMessage()
    ]);
}
?>

