<?php
require_once 'db_connection.php';
header('Content-Type: application/json');

// Verificar que la solicitud sea POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => 'Método no permitido']);
    exit;
}

// Obtener los datos enviados
$data = json_decode(file_get_contents('php://input'), true);

// Verificar si es una simulación o un proceso normal de DTE
$esSimulacion = isset($data['simulacion']) && $data['simulacion'] === true;

// Validar los datos según el tipo de operación
if ($esSimulacion) {
    // Para simulación, solo necesitamos productos
    if (!isset($data['productos']) || empty($data['productos'])) {
        http_response_code(400);
        echo json_encode(['status' => 'error', 'message' => 'No hay productos para simular la venta']);
        exit;
    }
    
    // Usar la referencia proporcionada o crear una por defecto
    $referencia = isset($data['referencia']) ? $data['referencia'] : 'Simulación de venta';
    
    // Obtener el ID de usuario y almacén
    $usuarioId = isset($data['usuario_id']) ? $data['usuario_id'] : 1;
    $almacenId = isset($data['almacen_id']) ? $data['almacen_id'] : 1;
} else {
    // Para DTE, necesitamos el ID del DTE y productos
    if (!isset($data['dte_id']) || !isset($data['productos'])) {
        http_response_code(400);
        echo json_encode(['status' => 'error', 'message' => 'Datos incompletos']);
        exit;
    }
    
    // Si no hay productos con ID, devolver un mensaje informativo
    if (empty($data['productos'])) {
        echo json_encode([
            'status' => 'info', 
            'message' => 'No hay productos para registrar salida de inventario',
            'dte_id' => $data['dte_id']
        ]);
        exit;
    }
}

try {
    $conn = getConnection();
    
    // Iniciar transacción
    $conn->beginTransaction();
    
    // Si es un DTE, obtener información para la referencia
    if (!$esSimulacion) {
        $stmtDTE = $conn->prepare("SELECT tipo_dte, folio FROM tb_facturas_dte WHERE id = :dte_id");
        $stmtDTE->bindParam(':dte_id', $data['dte_id']);
        $stmtDTE->execute();
        $dteInfo = $stmtDTE->fetch(PDO::FETCH_ASSOC);
        
        if (!$dteInfo) {
            throw new Exception("No se encontró el DTE con ID: " . $data['dte_id']);
        }
        
        // Crear la referencia para el movimiento
        $tipoDocumento = '';
        switch ($dteInfo['tipo_dte']) {
            case '33': $tipoDocumento = 'Factura'; break;
            case '34': $tipoDocumento = 'Factura Exenta'; break;
            case '39': $tipoDocumento = 'Boleta'; break;
            case '61': $tipoDocumento = 'Nota de Crédito'; break;
            default: $tipoDocumento = 'DTE'; break;
        }
        $referencia = $tipoDocumento . ' #' . $dteInfo['folio'];
        
        // Obtener el ID de usuario y almacén
        $usuarioId = isset($data['usuario_id']) ? $data['usuario_id'] : 1;
        $almacenId = isset($data['almacen_id']) ? $data['almacen_id'] : 1;
    }
    
    // Preparar la consulta para insertar movimientos de inventario
    $stmt = $conn->prepare("INSERT INTO movimiento_inventario (
        repuesto_id, 
        tipo_movimiento, 
        cantidad, 
        fecha_movimiento, 
        usuario_id, 
        referencia,
        almacen_id,
        lote
    ) VALUES (
        :repuesto_id, 
        'SALIDA', 
        :cantidad, 
        NOW(), 
        :usuario_id, 
        :referencia,
        :almacen_id,
        :lote
    )");
    
    // Contadores para el reporte
    $productosRegistrados = 0;
    $productosOmitidos = 0;
    $errores = [];
    
    // Insertar cada movimiento de inventario
    foreach ($data['productos'] as $producto) {
        // Verificar que el producto tenga un ID válido
        if (empty($producto['repuesto_id'])) {
            $productosOmitidos++;
            continue; // Saltar este producto si no tiene ID
        }
        
        // Verificar que la cantidad sea válida
        $cantidad = intval($producto['cantidad']);
        if ($cantidad <= 0) {
            $productosOmitidos++;
            continue; // Saltar este producto si la cantidad no es válida
        }
        
        // Obtener los lotes disponibles para este producto (FIFO)
        $stmtLotes = $conn->prepare("
            SELECT lote, cantidad 
            FROM stock 
            WHERE repuesto_id = :repuesto_id 
            AND almacen_id = :almacen_id 
            AND cantidad > 0 
            ORDER BY fecha_ingreso ASC
        ");
        $stmtLotes->bindParam(':repuesto_id', $producto['repuesto_id']);
        $stmtLotes->bindParam(':almacen_id', $almacenId);
        $stmtLotes->execute();
        
        $lotes = $stmtLotes->fetchAll(PDO::FETCH_ASSOC);
        
        // Verificar si hay stock suficiente
        $stockTotal = array_sum(array_column($lotes, 'cantidad'));
        if ($stockTotal < $cantidad) {
            $errores[] = [
                'repuesto_id' => $producto['repuesto_id'],
                'nombre' => $producto['nombre'] ?? 'Producto #' . $producto['repuesto_id'],
                'cantidad_solicitada' => $cantidad,
                'stock_disponible' => $stockTotal
            ];
            continue; // Saltar este producto si no hay stock suficiente
        }
        
        // Cantidad pendiente por procesar
        $cantidadPendiente = $cantidad;
        
        // Actualizar el stock de cada lote (FIFO)
        foreach ($lotes as $lote) {
            $cantidadLote = min($cantidadPendiente, intval($lote['cantidad']));
            
            if ($cantidadLote <= 0) continue;
            
            // Registrar el movimiento de salida para este lote
            $stmt->bindParam(':repuesto_id', $producto['repuesto_id']);
            $stmt->bindParam(':cantidad', $cantidadLote);
            $stmt->bindParam(':usuario_id', $usuarioId);
            $stmt->bindParam(':referencia', $referencia);
            $stmt->bindParam(':almacen_id', $almacenId);
            $stmt->bindParam(':lote', $lote['lote']);
            
            $stmt->execute();
            
            // Actualizar el stock en la tabla stock
            $stmtUpdateStock = $conn->prepare("
                UPDATE stock 
                SET cantidad = cantidad - :cantidad 
                WHERE repuesto_id = :repuesto_id 
                AND almacen_id = :almacen_id 
                AND lote = :lote
            ");
            $stmtUpdateStock->bindParam(':cantidad', $cantidadLote);
            $stmtUpdateStock->bindParam(':repuesto_id', $producto['repuesto_id']);
            $stmtUpdateStock->bindParam(':almacen_id', $almacenId);
            $stmtUpdateStock->bindParam(':lote', $lote['lote']);
            $stmtUpdateStock->execute();
            
            $cantidadPendiente -= $cantidadLote;
            
            if ($cantidadPendiente <= 0) break;
        }
        
        $productosRegistrados++;
    }
    
    // Confirmar la transacción
    $conn->commit();
    
    // Preparar mensaje según los resultados
    $mensaje = "Se registraron salidas de inventario para $productosRegistrados productos";
    
    if ($productosOmitidos > 0) {
        $mensaje .= ". Se omitieron $productosOmitidos productos (sin ID o cantidad inválida)";
    }
    
    if (!empty($errores)) {
        $mensaje .= ". Algunos productos no tenían stock suficiente";
    }
    
    // Preparar la respuesta según el tipo de operación
    $respuesta = [
        'status' => 'success', 
        'message' => $mensaje,
        'productos_registrados' => $productosRegistrados,
        'productos_omitidos' => $productosOmitidos,
        'errores_stock' => $errores
    ];
    
    // Agregar el ID del DTE si no es una simulación
    if (!$esSimulacion && isset($data['dte_id'])) {
        $respuesta['dte_id'] = $data['dte_id'];
    }
    
    echo json_encode($respuesta);
    
} catch(Exception $e) {
    // Revertir la transacción en caso de error
    if (isset($conn)) {
        $conn->rollBack();
    }
    
    http_response_code(500);
    echo json_encode([
        'status' => 'error', 
        'message' => 'Error al registrar salidas de inventario: ' . $e->getMessage()
    ]);
}
?>

