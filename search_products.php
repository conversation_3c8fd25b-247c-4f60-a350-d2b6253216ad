<?php
// Configuración para depuración y manejo de errores
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', 'errors.log');
set_time_limit(30);

// Permitir CORS para desarrollo
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Accept, Cache-Control, Pragma');

// Si es una solicitud OPTIONS (preflight), responder inmediatamente
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('HTTP/1.1 200 OK');
    exit;
}

// Función para enviar respuesta JSON
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json; charset=utf-8');
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

try {
    session_start();
    require_once 'db_connection.php';

    // Verificar que el método de solicitud sea GET
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        sendJsonResponse([
            'status' => 'error',
            'message' => 'Método no permitido. Use GET.',
            'error_code' => 'METHOD_NOT_ALLOWED'
        ], 405);
    }

    // Verificar si el usuario está autenticado
    if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
        sendJsonResponse([
            'status' => 'error',
            'message' => 'No autorizado',
            'error_code' => 'UNAUTHORIZED'
        ], 401);
    }

    // Obtener término de búsqueda
    $searchTerm = isset($_GET['term']) ? trim($_GET['term']) : '';

    if (strlen($searchTerm) < 2) {
        sendJsonResponse([
            'status' => 'success',
            'data' => [],
            'message' => 'Término de búsqueda muy corto'
        ]);
    }

    // Obtener conexión a la base de datos
    $conn = getConnection();

    // Verificar que la conexión sea válida
    if (!($conn instanceof PDO)) {
        sendJsonResponse([
            'status' => 'error',
            'message' => 'Conexión a base de datos inválida',
            'error_code' => 'INVALID_CONNECTION'
        ], 500);
    }

    // Preparar consulta SQL optimizada
    $sql = "SELECT 
                r.id,
                r.nombre,
                r.descripcion,
                r.sku,
                r.precio_venta,
                r.url_imagen,
                r.fabricante,
                c.nombre AS categoria_nombre,
                COALESCE(
                    (SELECT SUM(s.cantidad) 
                     FROM stock s 
                     WHERE s.repuesto_id = r.id), 
                    0
                ) AS stock_actual
            FROM repuesto r
            LEFT JOIN categoria_repuesto c ON r.categoria_id = c.id
            WHERE r.activo = 1
            AND (
                r.nombre LIKE :search1
                OR r.sku LIKE :search2
                OR r.descripcion LIKE :search3
                OR r.codigo_fabricante LIKE :search4
                OR r.fabricante LIKE :search5
            )
            ORDER BY 
                CASE 
                    WHEN r.sku = :exact_term THEN 1
                    WHEN r.sku LIKE :start_term THEN 2
                    ELSE 3
                END,
                r.nombre ASC
            LIMIT 20";
    
    try {
        $stmt = $conn->prepare($sql);
        $searchPattern = '%' . $searchTerm . '%';
        $startPattern = $searchTerm . '%';
        
        $stmt->execute([
            'search1' => $searchPattern,
            'search2' => $searchPattern,
            'search3' => $searchPattern,
            'search4' => $searchPattern,
            'search5' => $searchPattern,
            'exact_term' => $searchTerm,
            'start_term' => $startPattern
        ]);
        
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Procesar los resultados
        $processedProducts = [];
        foreach ($products as $row) {
            $processedProducts[] = [
                'id' => $row['id'],
                'nombre' => $row['nombre'],
                'descripcion' => $row['descripcion'],
                'sku' => $row['sku'],
                'precio_venta' => floatval($row['precio_venta']),
                'url_imagen' => $row['url_imagen'],
                'categoria_nombre' => $row['categoria_nombre'],
                'stock_actual' => intval($row['stock_actual'])
            ];
        }
        
        sendJsonResponse([
            'status' => 'success',
            'data' => $processedProducts,
            'count' => count($processedProducts),
            'search_term' => $searchTerm
        ]);
        
    } catch (PDOException $e) {
        sendJsonResponse([
            'status' => 'error',
            'message' => 'Error en la consulta SQL',
            'error_code' => 'SQL_ERROR',
            'error_details' => $e->getMessage()
        ], 500);
    }

} catch (PDOException $e) {
    // Error específico de base de datos
    sendJsonResponse([
        'status' => 'error',
        'message' => 'Error de conexión a la base de datos',
        'error_code' => 'DB_ERROR',
        'error_details' => $e->getMessage()
    ], 500);

} catch (Exception $e) {
    // Error general
    sendJsonResponse([
        'status' => 'error',
        'message' => 'Error al procesar la solicitud',
        'error_code' => 'GENERAL_ERROR',
        'error_details' => $e->getMessage()
    ], 500);
}
?>