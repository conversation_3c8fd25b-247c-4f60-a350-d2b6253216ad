[2025-07-13 17:23:48] Iniciando script registrar_salida_inventario.php
[2025-07-13 17:23:48] Input recibido - {"simulacion":true,"productos":[{"repuesto_id":"56","nombre":"Valvulas Escape 4U","cantidad":1,"precio":27000}],"usuario_id":1,"almacen_id":"1","referencia":"Simulación de venta - 13/7/2025, 11:23:48"}
[2025-07-13 17:23:48] Datos decodificados - Array
(
    [simulacion] => 1
    [productos] => Array
        (
            [0] => Array
                (
                    [repuesto_id] => 56
                    [nombre] => Valvulas Escape 4U
                    [cantidad] => 1
                    [precio] => 27000
                )

        )

    [usuario_id] => 1
    [almacen_id] => 1
    [referencia] => Simulación de venta - 13/7/2025, 11:23:48
)

[2025-07-13 17:23:48] Es simulación: SÍ
[2025-07-13 17:23:48] Procesando simulación de venta
[2025-07-13 17:23:48] Productos recibidos: 1
[2025-07-13 17:23:48] Referencia: Simulación de venta - 13/7/2025, 11:23:48, Usuario ID: 1, Almacén ID: 1
[2025-07-13 17:23:48] Iniciando proceso de registro de salida de inventario
[2025-07-13 17:23:48] Conexión a base de datos establecida
[2025-07-13 17:23:48] Transacción iniciada
[2025-07-13 17:26:28] Iniciando script registrar_salida_inventario.php
[2025-07-13 17:26:28] Input recibido - {"simulacion":true,"productos":[{"repuesto_id":"56","nombre":"Valvulas Escape 4U","cantidad":1,"precio":27000}],"usuario_id":1,"almacen_id":"1","referencia":"Simulación de venta - 13/7/2025, 11:26:28"}
[2025-07-13 17:26:28] Datos decodificados - Array
(
    [simulacion] => 1
    [productos] => Array
        (
            [0] => Array
                (
                    [repuesto_id] => 56
                    [nombre] => Valvulas Escape 4U
                    [cantidad] => 1
                    [precio] => 27000
                )

        )

    [usuario_id] => 1
    [almacen_id] => 1
    [referencia] => Simulación de venta - 13/7/2025, 11:26:28
)

[2025-07-13 17:26:28] Es simulación: SÍ
[2025-07-13 17:26:28] Procesando simulación de venta
[2025-07-13 17:26:28] Productos recibidos: 1
[2025-07-13 17:26:28] Referencia: Simulación de venta - 13/7/2025, 11:26:28, Usuario ID: 1, Almacén ID: 1
[2025-07-13 17:26:28] Iniciando proceso de registro de salida de inventario
[2025-07-13 17:26:28] Conexión a base de datos establecida
[2025-07-13 17:26:28] Transacción iniciada
[2025-07-13 17:26:28] Iniciando procesamiento de productos
[2025-07-13 17:26:28] Procesando producto 0: Array
(
    [repuesto_id] => 56
    [nombre] => Valvulas Escape 4U
    [cantidad] => 1
    [precio] => 27000
)

[2025-07-13 17:26:28] Producto ID válido: 56
[2025-07-13 17:26:28] Cantidad válida: 1
[2025-07-13 17:26:28] Consultando lotes para repuesto_id: 56, almacen_id: 1
[2025-07-13 17:26:29] Lotes encontrados: 1 - Array
(
    [0] => Array
        (
            [lote] => LOT-20250522-0040
            [cantidad] => 4
        )

)

[2025-07-13 17:26:29] Stock total disponible: 4, cantidad requerida: 1
[2025-07-13 17:30:03] Iniciando script registrar_salida_inventario.php
[2025-07-13 17:30:03] Input recibido - {"simulacion":true,"productos":[{"repuesto_id":"56","nombre":"Valvulas Escape 4U","cantidad":1,"precio":27000}],"usuario_id":1,"almacen_id":"1","referencia":"Simulación de venta - 13/7/2025, 11:30:03"}
[2025-07-13 17:30:03] Datos decodificados - Array
(
    [simulacion] => 1
    [productos] => Array
        (
            [0] => Array
                (
                    [repuesto_id] => 56
                    [nombre] => Valvulas Escape 4U
                    [cantidad] => 1
                    [precio] => 27000
                )

        )

    [usuario_id] => 1
    [almacen_id] => 1
    [referencia] => Simulación de venta - 13/7/2025, 11:30:03
)

[2025-07-13 17:30:03] Es simulación: SÍ
[2025-07-13 17:30:03] Procesando simulación de venta
[2025-07-13 17:30:03] Productos recibidos: 1
[2025-07-13 17:30:03] Referencia: Simulación de venta - 13/7/2025, 11:30:03, Usuario ID: 1, Almacén ID: 1
[2025-07-13 17:30:03] Iniciando proceso de registro de salida de inventario
[2025-07-13 17:30:03] Conexión a base de datos establecida
[2025-07-13 17:30:03] Transacción iniciada
[2025-07-13 17:30:03] Iniciando procesamiento de productos
[2025-07-13 17:30:03] Procesando producto 0: Array
(
    [repuesto_id] => 56
    [nombre] => Valvulas Escape 4U
    [cantidad] => 1
    [precio] => 27000
)

[2025-07-13 17:30:03] Producto ID válido: 56
[2025-07-13 17:30:03] Cantidad válida: 1
[2025-07-13 17:30:03] Consultando lotes para repuesto_id: 56, almacen_id: 1
[2025-07-13 17:30:03] Lotes encontrados: 1 - Array
(
    [0] => Array
        (
            [lote] => LOT-20250522-0040
            [cantidad] => 4
        )

)

[2025-07-13 17:30:03] Stock total disponible: 4, cantidad requerida: 1
[2025-07-13 17:33:12] Iniciando script registrar_salida_inventario.php
[2025-07-13 17:33:12] Input recibido - {"simulacion":true,"productos":[{"repuesto_id":"56","nombre":"Valvulas Escape 4U","cantidad":1,"precio":27000}],"usuario_id":1,"almacen_id":"1","referencia":"Simulación de venta - 13/7/2025, 11:33:12"}
[2025-07-13 17:33:12] Datos decodificados - Array
(
    [simulacion] => 1
    [productos] => Array
        (
            [0] => Array
                (
                    [repuesto_id] => 56
                    [nombre] => Valvulas Escape 4U
                    [cantidad] => 1
                    [precio] => 27000
                )

        )

    [usuario_id] => 1
    [almacen_id] => 1
    [referencia] => Simulación de venta - 13/7/2025, 11:33:12
)

[2025-07-13 17:33:12] Es simulación: SÍ
[2025-07-13 17:33:12] Procesando simulación de venta
[2025-07-13 17:33:12] Productos recibidos: 1
[2025-07-13 17:33:12] Referencia: Simulación de venta - 13/7/2025, 11:33:12, Usuario ID: 1, Almacén ID: 1
[2025-07-13 17:33:12] Iniciando proceso de registro de salida de inventario
[2025-07-13 17:33:12] Conexión a base de datos establecida
[2025-07-13 17:33:12] Transacción iniciada
[2025-07-13 17:33:12] Iniciando procesamiento de productos
[2025-07-13 17:33:12] Procesando producto 0: Array
(
    [repuesto_id] => 56
    [nombre] => Valvulas Escape 4U
    [cantidad] => 1
    [precio] => 27000
)

[2025-07-13 17:33:12] Producto ID válido: 56
[2025-07-13 17:33:12] Cantidad válida: 1
[2025-07-13 17:33:12] Consultando lotes para repuesto_id: 56, almacen_id: 1
[2025-07-13 17:33:12] Lotes encontrados: 1 - Array
(
    [0] => Array
        (
            [lote] => LOT-20250522-0040
            [cantidad] => 4
        )

)

[2025-07-13 17:33:12] Stock total disponible: 4, cantidad requerida: 1
[2025-07-13 17:33:12] Iniciando actualización de lotes
[2025-07-13 17:33:12] Procesando lote: LOT-20250522-0040, cantidad lote: 1
[2025-07-13 17:33:12] Registrando movimiento de inventario
[2025-07-13 17:33:12] Movimiento de inventario registrado exitosamente
[2025-07-13 17:33:12] Actualizando stock en tabla stock
[2025-07-13 17:33:12] Stock actualizado exitosamente
[2025-07-13 17:33:12] Cantidad pendiente restante: 0
[2025-07-13 17:33:12] Producto procesado exitosamente
[2025-07-13 17:35:38] Iniciando script registrar_salida_inventario.php
[2025-07-13 17:35:38] Input recibido - {"simulacion":true,"productos":[{"repuesto_id":"56","nombre":"Valvulas Escape 4U","cantidad":1,"precio":27000}],"usuario_id":1,"almacen_id":"1","referencia":"Simulación de venta - 13/7/2025, 11:35:38"}
[2025-07-13 17:35:38] Datos decodificados - Array
(
    [simulacion] => 1
    [productos] => Array
        (
            [0] => Array
                (
                    [repuesto_id] => 56
                    [nombre] => Valvulas Escape 4U
                    [cantidad] => 1
                    [precio] => 27000
                )

        )

    [usuario_id] => 1
    [almacen_id] => 1
    [referencia] => Simulación de venta - 13/7/2025, 11:35:38
)

[2025-07-13 17:35:38] Es simulación: SÍ
[2025-07-13 17:35:38] Procesando simulación de venta
[2025-07-13 17:35:38] Productos recibidos: 1
[2025-07-13 17:35:38] Referencia: Simulación de venta - 13/7/2025, 11:35:38, Usuario ID: 1, Almacén ID: 1
[2025-07-13 17:35:38] Iniciando proceso de registro de salida de inventario
[2025-07-13 17:35:39] Conexión a base de datos establecida
[2025-07-13 17:35:39] Transacción iniciada
[2025-07-13 17:35:39] Iniciando procesamiento de productos
[2025-07-13 17:35:39] Procesando producto 0: Array
(
    [repuesto_id] => 56
    [nombre] => Valvulas Escape 4U
    [cantidad] => 1
    [precio] => 27000
)

[2025-07-13 17:35:39] Producto ID válido: 56
[2025-07-13 17:35:39] Cantidad válida: 1
[2025-07-13 17:35:39] Consultando lotes para repuesto_id: 56, almacen_id: 1
[2025-07-13 17:35:39] Lotes encontrados: 1 - Array
(
    [0] => Array
        (
            [lote] => LOT-20250522-0040
            [cantidad] => 2
        )

)

[2025-07-13 17:35:39] Stock total disponible: 2, cantidad requerida: 1
[2025-07-13 17:35:39] Iniciando actualización de lotes
[2025-07-13 17:35:39] Procesando lote: LOT-20250522-0040, cantidad lote: 1
[2025-07-13 17:35:39] Registrando movimiento de inventario
[2025-07-13 17:35:39] Movimiento de inventario registrado exitosamente
[2025-07-13 17:35:39] Actualizando stock en tabla stock
[2025-07-13 17:35:39] Stock actualizado exitosamente
[2025-07-13 17:35:39] Cantidad pendiente restante: 0
[2025-07-13 17:35:39] Producto procesado exitosamente
[2025-07-13 17:37:16] Iniciando script registrar_salida_inventario.php
[2025-07-13 17:37:16] Input recibido - {"simulacion":true,"productos":[{"repuesto_id":"56","nombre":"Valvulas Escape 4U","cantidad":1,"precio":27000}],"usuario_id":1,"almacen_id":"1","referencia":"Simulación de venta - 13/7/2025, 11:37:16"}
[2025-07-13 17:37:16] Datos decodificados - Array
(
    [simulacion] => 1
    [productos] => Array
        (
            [0] => Array
                (
                    [repuesto_id] => 56
                    [nombre] => Valvulas Escape 4U
                    [cantidad] => 1
                    [precio] => 27000
                )

        )

    [usuario_id] => 1
    [almacen_id] => 1
    [referencia] => Simulación de venta - 13/7/2025, 11:37:16
)

[2025-07-13 17:37:16] Es simulación: SÍ
[2025-07-13 17:37:16] Procesando simulación de venta
[2025-07-13 17:37:16] Productos recibidos: 1
[2025-07-13 17:37:16] Referencia: Simulación de venta - 13/7/2025, 11:37:16, Usuario ID: 1, Almacén ID: 1
[2025-07-13 17:37:16] Iniciando proceso de registro de salida de inventario
[2025-07-13 17:37:16] Conexión a base de datos establecida
[2025-07-13 17:37:16] Transacción iniciada
[2025-07-13 17:37:16] Iniciando procesamiento de productos
[2025-07-13 17:37:16] Procesando producto 0: Array
(
    [repuesto_id] => 56
    [nombre] => Valvulas Escape 4U
    [cantidad] => 1
    [precio] => 27000
)

[2025-07-13 17:37:16] Producto ID válido: 56
[2025-07-13 17:37:16] Cantidad válida: 1
[2025-07-13 17:37:16] Consultando lotes para repuesto_id: 56, almacen_id: 1
[2025-07-13 17:37:16] Lotes encontrados: 1 - Array
(
    [0] => Array
        (
            [lote] => LOT-20250522-0040
            [cantidad] => 4
        )

)

[2025-07-13 17:37:16] Stock total disponible: 4, cantidad requerida: 1
[2025-07-13 17:37:16] Iniciando actualización de lotes
[2025-07-13 17:37:16] Procesando lote: LOT-20250522-0040, cantidad lote: 1
[2025-07-13 17:37:16] Registrando movimiento de inventario
[2025-07-13 17:37:16] Movimiento de inventario registrado exitosamente
[2025-07-13 17:37:16] Actualizando stock en tabla stock
[2025-07-13 17:37:16] Stock actualizado exitosamente
[2025-07-13 17:37:16] Cantidad pendiente restante: 0
[2025-07-13 17:37:16] Producto procesado exitosamente
