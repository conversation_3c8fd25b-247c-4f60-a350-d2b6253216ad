[2025-07-13 17:57:26] Iniciando script registrar_salida_inventario.php
[2025-07-13 17:57:26] Input recibido - {"simulacion":true,"productos":[{"repuesto_id":"56","nombre":"Valvulas Escape 4U","cantidad":1,"precio":27000}],"usuario_id":1,"almacen_id":"1","referencia":"Simulación de venta - 13/7/2025, 11:57:26"}
[2025-07-13 17:57:26] Datos decodificados - Array
(
    [simulacion] => 1
    [productos] => Array
        (
            [0] => Array
                (
                    [repuesto_id] => 56
                    [nombre] => Valvulas Escape 4U
                    [cantidad] => 1
                    [precio] => 27000
                )

        )

    [usuario_id] => 1
    [almacen_id] => 1
    [referencia] => Simulación de venta - 13/7/2025, 11:57:26
)

[2025-07-13 17:57:26] Es simulación: SÍ
[2025-07-13 17:57:26] Procesando simulación de venta
[2025-07-13 17:57:26] Productos recibidos: 1
[2025-07-13 17:57:26] Referencia: Simulación de venta - 13/7/2025, 11:57:26, Usuario ID: 1, Almacén ID: 1
[2025-07-13 17:57:26] Iniciando proceso de registro de salida de inventario
[2025-07-13 17:57:26] Conexión a base de datos establecida
[2025-07-13 17:57:26] Transacción iniciada
[2025-07-13 17:57:26] Iniciando procesamiento de productos
[2025-07-13 17:57:26] Procesando producto 0: Array
(
    [repuesto_id] => 56
    [nombre] => Valvulas Escape 4U
    [cantidad] => 1
    [precio] => 27000
)

[2025-07-13 17:57:26] Producto ID válido: 56
[2025-07-13 17:57:26] Cantidad válida: 1
[2025-07-13 17:57:26] Consultando lotes para repuesto_id: 56, almacen_id: 1
[2025-07-13 17:57:26] Lotes encontrados: 1 - Array
(
    [0] => Array
        (
            [lote] => LOT-20250522-0040
            [cantidad] => 4
        )

)

[2025-07-13 17:57:26] Stock total disponible: 4, cantidad requerida: 1
[2025-07-13 17:57:26] Iniciando actualización de lotes
[2025-07-13 17:57:26] Procesando lote: LOT-20250522-0040, cantidad lote: 1
[2025-07-13 17:57:26] Registrando movimiento de inventario
[2025-07-13 17:57:26] Movimiento de inventario registrado exitosamente
[2025-07-13 17:57:26] Actualizando stock en tabla stock
[2025-07-13 17:57:26] Valores para actualización: cantidadLote=1, repuesto_id=56, almacen_id=1, lote=LOT-20250522-0040
[2025-07-13 17:57:26] Stock antes de actualización: 3
[2025-07-13 17:57:26] Stock actualizado exitosamente. Filas afectadas: 1
[2025-07-13 17:57:26] Stock después de actualización: 2
[2025-07-13 17:57:26] Diferencia calculada: 1 (debería ser 1)
[2025-07-13 17:57:26] Cantidad pendiente restante: 0
[2025-07-13 17:57:26] Producto procesado exitosamente
[2025-07-13 18:02:19] Iniciando script registrar_salida_inventario.php
[2025-07-13 18:02:19] Input recibido - {"simulacion":true,"productos":[{"repuesto_id":"56","nombre":"Valvulas Escape 4U","cantidad":1,"precio":27000}],"usuario_id":1,"almacen_id":"1","referencia":"Simulación de venta - 13/7/2025, 12:02:19"}
[2025-07-13 18:02:19] Datos decodificados - Array
(
    [simulacion] => 1
    [productos] => Array
        (
            [0] => Array
                (
                    [repuesto_id] => 56
                    [nombre] => Valvulas Escape 4U
                    [cantidad] => 1
                    [precio] => 27000
                )

        )

    [usuario_id] => 1
    [almacen_id] => 1
    [referencia] => Simulación de venta - 13/7/2025, 12:02:19
)

[2025-07-13 18:02:19] Es simulación: SÍ
[2025-07-13 18:02:19] Procesando simulación de venta
[2025-07-13 18:02:19] Productos recibidos: 1
[2025-07-13 18:02:19] Referencia: Simulación de venta - 13/7/2025, 12:02:19, Usuario ID: 1, Almacén ID: 1
[2025-07-13 18:02:19] Iniciando proceso de registro de salida de inventario
[2025-07-13 18:02:20] Conexión a base de datos establecida
[2025-07-13 18:02:20] Transacción iniciada
[2025-07-13 18:02:20] Iniciando procesamiento de productos
[2025-07-13 18:02:20] Procesando producto 0: Array
(
    [repuesto_id] => 56
    [nombre] => Valvulas Escape 4U
    [cantidad] => 1
    [precio] => 27000
)

[2025-07-13 18:02:20] Producto ID válido: 56
[2025-07-13 18:02:20] Cantidad válida: 1
[2025-07-13 18:02:20] Consultando lotes para repuesto_id: 56, almacen_id: 1
[2025-07-13 18:02:20] Lotes encontrados: 1 - Array
(
    [0] => Array
        (
            [lote] => LOT-20250522-0040
            [cantidad] => 4
        )

)

[2025-07-13 18:02:20] Stock total disponible: 4, cantidad requerida: 1
[2025-07-13 18:02:20] Iniciando actualización de lotes
[2025-07-13 18:02:20] Procesando lote: LOT-20250522-0040, cantidad lote: 1
[2025-07-13 18:02:20] Registrando movimiento de inventario
[2025-07-13 18:02:20] Movimiento de inventario registrado exitosamente
[2025-07-13 18:02:20] Actualizando stock en tabla stock
[2025-07-13 18:02:20] Valores para actualización: cantidadLote=1, repuesto_id=56, almacen_id=1, lote=LOT-20250522-0040
[2025-07-13 18:02:20] Stock antes de actualización: 3
[2025-07-13 18:02:20] Stock actualizado exitosamente. Filas afectadas: 1
[2025-07-13 18:02:21] Stock después de actualización: 2
[2025-07-13 18:02:21] Diferencia calculada: 1 (debería ser 1)
[2025-07-13 18:02:21] Cantidad pendiente restante: 0
[2025-07-13 18:02:21] Producto procesado exitosamente
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.0996] === INICIANDO SCRIPT registrar_salida_inventario.php ===
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.101] REQUEST_METHOD: POST
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.1023] HTTP_USER_AGENT: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.1037] REMOTE_ADDR: ::1
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.1051] Input recibido - {"simulacion":true,"productos":[{"repuesto_id":"56","nombre":"Valvulas Escape 4U","cantidad":1,"precio":27000}],"usuario_id":1,"almacen_id":"1","referencia":"Simulación de venta - 13/7/2025, 13:22:40"}
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.1063] Datos decodificados - Array
(
    [simulacion] => 1
    [productos] => Array
        (
            [0] => Array
                (
                    [repuesto_id] => 56
                    [nombre] => Valvulas Escape 4U
                    [cantidad] => 1
                    [precio] => 27000
                )

        )

    [usuario_id] => 1
    [almacen_id] => 1
    [referencia] => Simulación de venta - 13/7/2025, 13:22:40
)

[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.1076] Es simulación: SÍ
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.1085] Procesando simulación de venta
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.1096] Productos recibidos: 1
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.1105] Referencia: Simulación de venta - 13/7/2025, 13:22:40, Usuario ID: 1, Almacén ID: 1
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.112] Iniciando proceso de registro de salida de inventario
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.4048] Conexión a base de datos establecida
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.4553] Transacción iniciada
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.5072] Iniciando procesamiento de productos
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.509] Procesando producto 0: Array
(
    [repuesto_id] => 56
    [nombre] => Valvulas Escape 4U
    [cantidad] => 1
    [precio] => 27000
)

[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.5108] Producto ID válido: 56
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.5123] Cantidad válida: 1
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.5141] Consultando lotes para repuesto_id: 56, almacen_id: 1
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.6234] Lotes encontrados: 1 - Array
(
    [0] => Array
        (
            [lote] => LOT-20250522-0040
            [cantidad] => 4
        )

)

[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.6253] Stock total disponible: 4, cantidad requerida: 1
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.6273] Iniciando actualización de lotes
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.629] Procesando lote: LOT-20250522-0040, cantidad lote: 1
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.6311] Registrando movimiento de inventario
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.6755] Movimiento de inventario registrado exitosamente
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.6779] Actualizando stock en tabla stock
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.6799] Valores para actualización: cantidadLote=1, repuesto_id=56, almacen_id=1, lote=LOT-20250522-0040
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.7755] Stock antes de actualización: 3
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.8633] Stock actualizado exitosamente. Filas afectadas: 1
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.9631] Stock después de actualización: 2
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.9656] Diferencia calculada: 1 (debería ser 1)
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.9681] Cantidad pendiente restante: 0
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.9702] Producto procesado exitosamente
[2025-07-13 19:22:42] [PID:20188] [TIME:1752427362.0195] === SCRIPT COMPLETADO EXITOSAMENTE ===
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8693] === INICIANDO SCRIPT registrar_salida_inventario.php ===
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8711] REQUEST_METHOD: POST
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8725] HTTP_USER_AGENT: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8739] REMOTE_ADDR: ::1
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8751] Input recibido - {"simulacion":true,"productos":[{"repuesto_id":"56","nombre":"Valvulas Escape 4U","cantidad":1,"precio":27000}],"usuario_id":1,"almacen_id":"1","referencia":"Simulación de venta - 13/7/2025, 13:43:20"}
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8765] Datos decodificados - Array
(
    [simulacion] => 1
    [productos] => Array
        (
            [0] => Array
                (
                    [repuesto_id] => 56
                    [nombre] => Valvulas Escape 4U
                    [cantidad] => 1
                    [precio] => 27000
                )

        )

    [usuario_id] => 1
    [almacen_id] => 1
    [referencia] => Simulación de venta - 13/7/2025, 13:43:20
)

[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8779] Es simulación: SÍ
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8793] Procesando simulación de venta
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8811] Productos recibidos: 1
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8823] Referencia: Simulación de venta - 13/7/2025, 13:43:20, Usuario ID: 1, Almacén ID: 1
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8839] Iniciando proceso de registro de salida de inventario
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.1664] Conexión a base de datos establecida
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.214] Transacción iniciada
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.2634] Iniciando procesamiento de productos
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.2646] Procesando producto 0: Array
(
    [repuesto_id] => 56
    [nombre] => Valvulas Escape 4U
    [cantidad] => 1
    [precio] => 27000
)

[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.2657] Producto ID válido: 56
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.2666] Cantidad válida: 1
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.2675] Consultando lotes para repuesto_id: 56, almacen_id: 1
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.3585] Lotes encontrados: 1 - Array
(
    [0] => Array
        (
            [lote] => LOT-20250522-0040
            [cantidad] => 2
        )

)

[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.3593] Stock total disponible: 2, cantidad requerida: 1
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.3601] Iniciando actualización de lotes
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.361] Procesando lote: LOT-20250522-0040, cantidad lote: 1
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.362] Registrando movimiento de inventario
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.4324] Movimiento de inventario registrado exitosamente
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.4336] Actualizando stock en tabla stock
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.4348] Valores para actualización: cantidadLote=1, repuesto_id=56, almacen_id=1, lote=LOT-20250522-0040
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.5286] Stock antes de actualización: 1
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.6194] Stock actualizado exitosamente. Filas afectadas: 1
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.7064] Stock después de actualización: 0
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.7081] Diferencia calculada: 1 (debería ser 1)
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.7097] Cantidad pendiente restante: 0
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.7116] Producto procesado exitosamente
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.7526] === SCRIPT COMPLETADO EXITOSAMENTE ===
