/* Variables globales de colores */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #e74c3c;
    --accent-color: #3498db;
    --hover-color: #e67e22;
}

/* Estilos base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}



/* Estilos del sub-header con buscadores */
.sub-header {
    background: rgba(44, 62, 80, 0.95);
    padding: 1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 75px; /* Altura del header principal ajustada */
    z-index: 999; /* Menor que el header principal */
}

.filter-toggle {
    display: none;
    background: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
    .filter-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .search-bar {
        max-height: 500px;
        overflow: hidden;
        transition: max-height 0.3s ease-out;
    }

    .search-bar.collapsed {
        max-height: 0;
        padding: 0;
        margin: 0;
    }
}

.search-bar {
    display: flex;
    gap: 0.8rem;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0.8rem;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
}

.search-bar select,
.search-bar input {
    padding: 0.8rem;
    border-radius: 6px;
    border: none;
    font-size: 1rem;
    background: white;
    height: 38px; /* Altura fija para coincidir con los botones */
    box-sizing: border-box;
}

.search-realtime input {
    padding: 0.8rem;
    border-radius: 6px;
    border: none;
    font-size: 1rem;
    background: white;
    width: 100%;
}

.search-bar select {
    min-width: 140px;
    cursor: pointer;
}

.search-input-wrapper {
    position: relative;
    flex-grow: 1;
}

.search-input-wrapper input {
    width: 100%;
    padding-right: 40px;
}

.search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
    cursor: pointer;
}

/* Estilos para los botones de búsqueda y reset */
.search-button, .reset-button {
    height: 44px;
    min-width: 44px;
    border: 1px solid #ccc;
    border-radius: 6px;
    background-color: #3498db;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 15px;
    transition: all 0.3s ease;
    box-sizing: border-box;
    gap: 8px;
}

.search-button i {
    color: #ffffff;
    font-size: 18px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.reset-button i {
    color: #ffeb3b;
    font-size: 18px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.search-button:hover, .reset-button:hover {
    background-color: #2980b9;
}

.reset-button {
    background-color: #95a5a6;
}

.reset-button:hover {
    background-color: #7f8c8d;
}

/* Estilos para el mensaje de no resultados */
.no-results {
    margin: 20px 0;
    text-align: center;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Estilos del showcase de marcas */
#brands-showcase {
    padding: 2rem 0;
    background: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    width: 100%;
    overflow: hidden;
    position: relative;
}

.brands-row {
    display: flex;
    align-items: center;
    animation: slideLogos 30s linear infinite;
    width: max-content;
    gap: 4rem;
    padding: 1rem 2rem;
}

.brands-row img {
    height: 80px;
    width: auto;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.brands-row img:hover {
    transform: scale(1.1);
}

@keyframes slideLogos {
    0% {
        transform: translateX(0);
    }

    100% {
        transform: translateX(-50%);
    }
}

/* Controles de vista */
.view-controls {
    max-width: 1400px;
    margin: 1rem auto;
    padding: 0 1rem;
    display: flex;
    gap: 1rem;
}

.view-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--primary-color);
    background: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.view-btn.active {
    background: var(--primary-color);
    color: white;
}

/* Estilos de tabla */
.products-table {
    display: none;
}

.products-table.active {
    display: table;
}

/* Estilos para el contenedor de productos */
.products-container {
    max-width: 1400px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.products-grid {
    display: none;
    grid-template-columns: repeat(7, 1fr);
    gap: 1.5rem;
    padding: 1rem;
    max-width: 1800px;
    margin: 0 auto;
}

.products-grid.active {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); /* Reducido de 250px a 200px */
    gap: 1.5rem;
}

.product-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    overflow: hidden;
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-image {
    width: 100%;
    height: 150px;
    object-fit: contain;
}

.product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.product-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.product-brand,
.product-model,
.product-year {
    margin: 0;
    font-size: 0.9rem;
}

.product-brand {
    font-weight: 600;
    color: var(--primary-color);
}

.product-model {
    color: #666;
    margin: 0.5rem 0;
}

.product-year {
    color: var(--secondary-color);
    font-weight: 500;
}

.product-description {
    font-size: 0.9rem;
    color: #777;
    margin: 0.5rem 0;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 1rem 0;
}

.quantity-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
}

.quantity-display {
    font-weight: 600;
}

.add-to-cart-btn {
    width: 100%;
    padding: 0.8rem;
    background: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.add-to-cart-btn:hover {
    background: var(--hover-color);
}

/* Estilos para el carrito */
.cart-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.cart-overlay.active {
    opacity: 1;
    visibility: visible;
}

.cart-modal {
    position: fixed;
    top: 0;
    right: -50%;
    width: 28%;
    height: 100vh;
    background: white;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    z-index: 1001;
}

.cart-modal.active {
    right: 0;
}

.cart-header {
    padding: 1rem;
    background: var(--primary-color);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.close-cart {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

.cart-items {
    padding: 1rem;
    max-height: calc(100vh - 500px); /* Adjusted for checkout form */
    overflow-y: auto;
    margin-bottom: 1rem;
}

.checkout-form {
    position: sticky;
    bottom: 0;
    background: white;
    padding: 1rem;
    border-top: 1px solid #eee;
    z-index: 1;
}

.cart-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #eee;
    position: relative;
}

.delete-item-btn {
    position: absolute;
    right: 10px;
    top: 10px;
    background: none;
    border: none;
    color: var(--secondary-color);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
}

.cart-quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 5px 0;
}

.cart-quantity-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
}


.cart-item-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
}

.cart-item-details {
    flex-grow: 1;
}

.cart-total {
    padding: 1rem;
    background: #f8f8f8;
    position: absolute;
    bottom: 0;
    width: 100%;
}

.nav-arrow {
    display: none;
}

.product-row {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE and Edge */
}

.product-row::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari and Opera */
}



@media (max-width: 768px) {
    header {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        background: var(--primary-color);
        padding: 0.5rem;
        z-index: 1001;
        height: auto;
    }

    .sub-header {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 1000;
        padding: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 1rem;
    }

    .filter-toggle {
        position: static;
        margin: 0;
    }



    .modules-menu {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        display: flex;
        justify-content: space-around;
        padding: 0.5rem;
        background: var(--primary-color);
        margin: 0;
        z-index: 1001;
    }

    .modules-menu li {
        padding: 0.5rem;
        background: none;
    }

    .modules-menu li span {
        display: none;
    }

    .modules-menu li i {
        font-size: 1.4rem;
    }



    body {
        padding-top: 100px;
        padding-bottom: 60px;
    }

    .sub-header {
        position: fixed;
        top: 45px;
        left: 0;
        width: 100%;
        z-index: 1000;
    }

    .filter-toggle {
        position: static;
        margin: 0 0.5rem;
    }

    .modules-menu {
        display: flex;
        justify-content: space-around;
        width: 100%;
        text-align: center;
    }

    .modules-menu li {
        font-size: 0.85rem;
        padding: 0.5rem;
    }

    .search-bar {
        flex-direction: column;
        width: 100%;
    }

    .search-bar select,
    .search-bar input {
        width: 100%;
    }



    .products-grid {
        display: block;
        padding: 1rem;
    }

    .row-container {
        position: relative;
        margin-bottom: 2rem;
        overflow: hidden;
        padding: 0 45px;
    }

    .product-row {
        display: flex;
        scroll-snap-type: x mandatory;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .product-row::-webkit-scrollbar {
        display: none;
    }

    .product-card {
        scroll-snap-align: start;
        min-width: 100%;
        margin-right: 15px;
    }

    .nav-arrow {
        display: flex;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(44, 62, 80, 0.8);
        color: white;
        border: none;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        cursor: pointer;
        z-index: 2;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        transition: background 0.3s ease;
    }

    .nav-arrow:hover {
        background: var(--hover-color);
    }

    .nav-arrow.left {
        left: 0;
    }

    .nav-arrow.right {
        right: 0;
    }

    .product-image {
        height: 200px;
    }

    .cart-modal {
        width: 100%;
        right: -100%;
    }

    .brands-row {
        animation: slideLogos 10s linear infinite;
        gap: 2rem;
    }

    .brands-row img {
        height: 50px;
    }

    .nav-arrow {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .brands-row {
        animation: slideLogos 25s linear infinite;
        gap: 1.5rem;
    }

    .brands-row img {
        height: 40px;
    }

    .nav-arrow {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}

.modern-input {
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 0.5rem;
    height: 38px; /* Altura fija para todos los inputs */
}

.modern-select {
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 0.5rem;
    height: 38px; /* Altura fija para todos los selects */
}

.discount-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.discount-type {
    width: 60px;
}

.discount-value {
    flex-grow: 1;
    width: 80px;
}

.checkout-btn {
    width: 100%;
    padding: 0.8rem;
    background: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.3s ease;
    margin-top: 0.5rem;
}

.checkout-btn:hover {
    background: var(--hover-color);
}

.client-section {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.client-section input {
    flex-grow: 1;
}

.client-section button {
    flex-shrink: 0;
    height: 100%;
}

/* Estilos para el canvas de JSON */
.dte-canvas {
    position: fixed;
    top: 0;
    right: -35%;
    width: 35%;
    height: 100%;
    background-color: #fff;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.2);
    transition: right 0.3s ease;
    z-index: 2000;
    overflow-y: auto;
    padding: 20px;
}

.dte-canvas.active {
    right: 0;
}

.canvas-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1999;
}

.canvas-overlay.active {
    opacity: 1;
    visibility: visible;
}

.dte-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.close-canvas {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
}

.dte-form-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.dte-form-section h3 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    color: var(--primary-color);
}

.form-row {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: flex-end; /* Align items to the bottom */
}

.form-group {
    flex: 1;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    font-size: 0.9rem;
}

.json-btn {
    background-color: var(--secondary-color);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s;
    display: flex;
    align-items: center;
    gap: 5px;
}

.json-btn:hover {
    background-color: var(--hover-color);
}

.readonly-field {
    background-color: #f0f0f0;
    cursor: not-allowed;
}

#jsonResult {
    background-color: #2c3e50;
    color: white;
    padding: 10px;
    border-radius: 5px;
    max-height: 200px;
    overflow: auto;
    margin-top: 20px;
    white-space: pre;
    display: none;
}

.add-item-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
}

.item-row {
    background-color: #fff;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 10px;
    position: relative;
}

.remove-item-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background: none;
    border: none;
    color: var(--secondary-color);
    cursor: pointer;
}

@media (max-width: 768px) {
    .dte-canvas {
        width: 100%;
        right: -100%;
    }

    .form-row {
        flex-direction: column;
        gap: 5px;
    }
}

#jsonResult {
background-color: #2c3e50;
color: white;
padding: 15px;
border-radius: 5px;
max-height: 400px;
overflow: auto;
margin-top: 20px;
white-space: pre;
font-family: 'Courier New', monospace;
font-size: 14px;
line-height: 1.5;
border: 1px solid #1c2e40;
box-shadow: inset 0 0 10px rgba(0,0,0,0.3);
display: none;
}

#jsonResult::-webkit-scrollbar {
width: 12px;
}

#jsonResult::-webkit-scrollbar-track {
background: #1c2e40;
border-radius: 5px;
}

#jsonResult::-webkit-scrollbar-thumb {
background-color: #e74c3c;
border-radius: 5px;
border: 3px solid #1c2e40;
}

.json-btn {
margin-bottom: 20px;
}

.json-copy-btn {
background-color: #3498db;
color: white;
border: none;
padding: 5px 10px;
border-radius: 3px;
cursor: pointer;
margin: 10px 0;
display: flex;
align-items: center;
gap: 5px;
font-size: 0.9rem;
}

.json-copy-btn:hover {
background-color: #2980b9;
}

.copy-message {
color: #2ecc71;
font-size: 0.9rem;
margin-left: 10px;
opacity: 0;
transition: opacity 0.3s;
}

.copy-message.visible {
opacity: 1;
}


.file-paths-info {
background-color: #f1f8ff;
border-left: 4px solid #3498db;
padding: 15px;
margin: 15px 0;
border-radius: 4px;
}

.file-paths-info p {
margin-top: 0;
margin-bottom: 10px;
color: #2c3e50;
}

.file-paths-info ul {
margin: 0;
padding-left: 25px;
}

.file-paths-info li {
margin-bottom: 5px;
color: #555;
}

.file-paths-info strong {
color: #2c3e50;
}

#apiToken {
background-color: #f8f9fa;
cursor: not-allowed;
}

#enviarDTEBtn {
margin-top: 15px;
padding: 12px 20px;
}

#apiResponse ul {
padding-left: 20px;
}

#apiResponse li {
margin-bottom: 8px;
}

#apiResponse pre {
white-space: pre-wrap;
word-break: break-word;
}


/* Add this to your existing styles */
.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    margin-bottom: 10px;
}

.checkbox-container input {
    margin-right: 8px;
    cursor: pointer;
}

.checkbox-label {
    font-weight: 500;
    color: var(--primary-color);
}

/* Add to your styles/controls.css or another appropriate file */
.input-with-button {
    display: flex;
    align-items: stretch; /* Asegura que ambos elementos tengan la misma altura */
    width: 100%;
}

.input-with-button .modern-input {
    flex: 1;
    height: 38px; /* Altura fija para coincidir con el botón */
    padding: 8px 12px; /* Ajusta el padding para que coincida con el botón */
    border: 1px solid #3498db; /* Ensure border width is consistent */
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    box-sizing: border-box; /* Include padding and border in the element's total width and height */
    font-size: 1rem; /* Asegura que el tamaño de fuente sea consistente */
}

.input-with-button .modern-input:focus {
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    outline: none; /* Remove default focus outline */
}

.input-with-button .modern-button {
    background-color: #3498db;
    color: white;
    border: 1px solid #3498db; /* Match input border */
    border-left: none; /* Remove left border to blend */
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    padding: 8px 15px; /* Ajusta el padding para que coincida con el input */
    display: flex;
    align-items: center; /* Centra el contenido verticalmente */
    justify-content: center; /* Centra el contenido horizontalmente */
    font-size: 1rem; /* Asegura que el tamaño de fuente sea consistente */
    cursor: pointer;
    transition: all 0.3s ease;
    height: 38px; /* Altura fija para coincidir con el input */
}

.input-with-button .modern-button:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

/* Optional: Apply box-sizing globally if not already done */
/*
* {
  box-sizing: border-box;
}
*/

/* For responsive layout */
@media (min-width: 768px) {
    .form-row .form-group:has(.input-with-button) {
        max-width: 300px;
    }
}


/* Estilos para el campo de input con botón de búsqueda */
.input-with-button {
    position: relative;
    display: flex;
    width: 100%;
}

.input-with-button .modern-input {
    flex-grow: 1;
    padding-right: 40px; /* Espacio para el botón */
}

.input-with-button .search-btn {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 35px;
    background-color: #4a90e2;
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

.input-with-button .search-btn:hover {
    background-color: #3a7bc8;
}

/* Estilos para el mensaje de notificación */
/* Estilos para notificaciones */
.notification {
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 4px;
    font-size: 14px;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.notification.success,
.notification.info {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.notification.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.notification.warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
}

.notification.visible {
    display: block;
    animation: fadeIn 0.3s, fadeOut 0.5s 3s forwards;
}

/* Contenedor de notificaciones */
#notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 350px;
}

/* Animaciones para notificaciones y contador */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes countUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.dte-count.updated {
    animation: countUpdate 0.3s ease-in-out;
}


.error {
    border-color: red !important;
}

.error-message {
    font-size: 12px;
    margin-top: 5px;
}

/* Ajustes para las tarjetas de productos */
.product-card {
    font-size: 0.75rem;
}

.product-header {
    gap: 0.3rem;
    margin-bottom: 0.3rem;
}

.product-brand,
.product-model,
.product-year {
    font-size: 0.75rem;
    margin: 0;
}

.product-brand {
    font-size: 0.8rem;
}

.product-description {
    font-size: 0.75rem;
    margin: 0.3rem 0;
}

.quantity-controls {
    gap: 0.5rem;
    margin: 0.5rem 0;
}

.quantity-btn {
    width: 24px;
    height: 24px;
    font-size: 0.75rem;
}

.quantity-display {
    font-size: 0.75rem;
}

.add-to-cart-btn {
    padding: 0.5rem;
    font-size: 0.75rem;
}

/* Ajustes para la tabla de productos */
.products-table table {
    font-size: 0.75rem;
}

.products-table th,
.products-table td {
    padding: 0.3rem 0.5rem;
    line-height: 1.2;
}

.products-table th {
    font-size: 0.8rem;
    font-weight: 600;
}

/* Ajustes para la barra de búsqueda */
.search-bar select,
.search-bar input {
    font-size: 0.75rem;
    padding: 0.5rem;
}

.search-realtime input {
    font-size: 0.75rem;
    padding: 0.5rem;
}

/* Ajustes para los controles de vista */
.view-btn {
    font-size: 0.75rem;
    padding: 0.3rem 0.8rem;
}

/* Ajustes para el carrito */
.cart-header {
    font-size: 0.8rem;
    padding: 0.8rem;
}

.cart-item-details {
    font-size: 0.75rem;
}

.cart-quantity-controls {
    font-size: 0.75rem;
}

.cart-quantity-btn {
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
}

/* Ajustes para formularios y botones */
.modern-input,
.modern-select {
    font-size: 0.75rem;
    padding: 0.4rem;
}

.json-btn {
    font-size: 0.75rem;
    padding: 0.5rem 0.8rem;
}

.form-group label {
    font-size: 0.75rem;
}

/* Ajustes para notificaciones y mensajes */
.error-message {
    font-size: 0.7rem;
}

/* Mantener la responsividad */
@media (max-width: 768px) {
    .product-card {
        font-size: 0.75rem;
    }

    .modules-menu li {
        font-size: 0.75rem;
    }
}

/* Estilos para el Canvas de Cotización */
.quote-canvas {
    position: fixed;
    top: 0;
    right: -40%;
    width: 40%;
    height: 100%;
    background-color: #fff;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.2);
    transition: right 0.3s ease;
    z-index: 2000;
    overflow-y: auto;
    padding: 20px;
}

.quote-canvas.active {
    right: 0;
}

.quote-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 3px solid #34495e;
}

.quote-header h2 {
    margin: 0;
    color: #34495e;
    font-size: 1.5rem;
    font-weight: 600;
}

.close-canvas {
    background: transparent;
    border: none;
    font-size: 1.8rem;
    color: #95a5a6;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-canvas:hover {
    background: #f8f9fa;
    color: #34495e;
}

.quote-form-section {
    background-color: #ffffff;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 10px;
    border: 1px solid #ecf0f1;
    box-shadow: 0 2px 4px rgba(0,0,0,0.03);
}

.quote-form-section h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #34495e;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.quote-form-section h3::before {
    content: '';
    width: 4px;
    height: 20px;
    background: #34495e;
    border-radius: 2px;
}

/* Tabs para alternar entre búsqueda manual y desde inventario */
.tabs-container {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 2px solid #ddd;
}

.tab-button {
    background: none;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    font-size: 0.9rem;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-button:hover {
    color: #333;
}

.tab-button.active {
    color: #007bff;
    border-bottom-color: #007bff;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Resultados de búsqueda */
.search-results {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    margin-top: 10px;
}

.search-result-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

/* Lista de productos en cotización */
.quote-items-container {
    min-height: 150px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 0;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.empty-state i {
    font-size: 3.5rem;
    margin-bottom: 15px;
    color: #ddd;
}

.quote-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    transition: background 0.2s ease;
}

.quote-item:hover {
    background-color: #f8f9fa;
}

.quote-item:last-child {
    border-bottom: none;
}

.quote-item-info {
    flex: 1;
    padding-right: 15px;
}

.quote-item-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.95rem;
    margin-bottom: 3px;
}

.quote-item-description {
    font-size: 0.8rem;
    color: #7f8c8d;
    margin-bottom: 5px;
}

.quote-item-price {
    font-size: 0.85rem;
    color: #34495e;
}

.quote-item-actions {
    display: flex;
    gap: 15px;
    align-items: center;
}

.quote-item-quantity {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #f8f9fa;
    border-radius: 20px;
    padding: 4px 8px;
}

.quote-item-quantity span {
    min-width: 30px;
    text-align: center;
    font-weight: 600;
    color: #34495e;
}

.quantity-btn {
    background: #34495e;
    color: white;
    border: none;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.quantity-btn:hover {
    background: #2c3e50;
    transform: scale(1.1);
}

.quantity-btn:active {
    transform: scale(0.95);
}

.remove-item-btn {
    background: transparent;
    color: #e74c3c;
    border: 1px solid #e74c3c;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.remove-item-btn:hover {
    background: #e74c3c;
    color: white;
    transform: scale(1.1);
}

.remove-item-btn i {
    font-size: 0.85rem;
}

.quote-item-actions .remove-item-btn {
    /* Override absolute positioning defined earlier */
    position: static;
    width: 26px;
    height: 26px;
    border: 1px solid #e74c3c;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    color: #e74c3c;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quote-item-actions .remove-item-btn:hover {
    background: #e74c3c;
    color: #fff;
    transform: scale(1.1);
}

/* Totales */
.quote-totals {
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    border-radius: 8px;
    color: white;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    font-size: 0.9rem;
    opacity: 0.9;
}

.total-row span:last-child {
    font-weight: 500;
}

.total-row.total-final {
    font-size: 1.2rem;
    font-weight: bold;
    color: white !important;
    padding-top: 12px;
    border-top: 2px solid rgba(255,255,255,0.3);
    margin-bottom: 0;
    opacity: 1;
}

/* Botones de acción */
.quote-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.action-button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

.action-button.primary {
    background: #28a745;
    color: white;
}

.action-button.primary:hover {
    background: #218838;
}

.action-button.secondary {
    background: #6c757d;
    color: white;
}

.action-button.secondary:hover {
    background: #5a6268;
}

/* Botón de agregar a cotización */
.add-to-quote-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    margin-left: 5px;
    transition: all 0.3s ease;
}

.add-to-quote-btn:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

.add-to-quote-btn i {
    font-size: 1rem;
}

/* Agrupar botones en tabla */
.table-view td:last-child {
    display: flex;
    gap: 5px;
    align-items: center;
    justify-content: flex-end;
}

/* Responsive para canvas de cotización */
@media (max-width: 768px) {
    .quote-canvas {
        width: 100%;
        right: -100%;
    }
    
    .add-to-quote-btn {
        padding: 6px 10px;
        font-size: 0.8rem;
    }
}
