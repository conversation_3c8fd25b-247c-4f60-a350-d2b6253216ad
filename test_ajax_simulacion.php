<?php
// Script para probar la simulación exactamente como la haría el frontend
// Simula una llamada AJAX para venta de 2 unidades

function logAjaxTest($mensaje) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] AJAX_TEST: $mensaje";
    file_put_contents('ajax_test.log', "$logMessage\n", FILE_APPEND | LOCK_EX);
    echo "$logMessage\n";
}

try {
    logAjaxTest("=== INICIANDO PRUEBA AJAX PARA 2 UNIDADES ===");
    
    // Datos que enviaría el frontend para una venta de 2 unidades
    $postData = [
        'simulacion' => true,
        'productos' => [
            [
                'repuesto_id' => '56',
                'nombre' => 'Valvulas Escape 4U',
                'cantidad' => 2,  // ¡AQUÍ ESTÁ LA DIFERENCIA! 2 unidades en lugar de 1
                'precio' => 27000
            ]
        ],
        'usuario_id' => 1,
        'almacen_id' => '1',
        'referencia' => 'Simulación de venta - ' . date('d/m/Y, H:i:s')
    ];
    
    logAjaxTest("Datos a enviar: " . json_encode($postData, JSON_PRETTY_PRINT));
    
    // Simular la llamada AJAX usando cURL
    $url = 'http://localhost:8080/tata%20repuestos/registrar_salida_inventario.php';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'X-Requested-With: XMLHttpRequest'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    logAjaxTest("Enviando petición AJAX...");
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        logAjaxTest("ERROR cURL: $error");
    } else {
        logAjaxTest("Código HTTP: $httpCode");
        logAjaxTest("Respuesta: $response");
        
        // Decodificar respuesta JSON
        $responseData = json_decode($response, true);
        if ($responseData) {
            logAjaxTest("Respuesta decodificada: " . json_encode($responseData, JSON_PRETTY_PRINT));
        }
    }
    
    logAjaxTest("=== PRUEBA AJAX COMPLETADA ===");
    
} catch (Exception $e) {
    logAjaxTest("ERROR: " . $e->getMessage());
}

// Instrucciones para el usuario
echo "\n";
echo "=== INSTRUCCIONES ===\n";
echo "1. Asegúrate de que el servidor web esté ejecutándose en localhost:8080\n";
echo "2. Verifica que el stock inicial del producto 56 sea 4 unidades\n";
echo "3. Ejecuta este script: php test_ajax_simulacion.php\n";
echo "4. Revisa los logs en ajax_test.log y simulacion_debug_nuevo.log\n";
echo "5. Verifica el stock final en la base de datos\n";
echo "\nSi el error persiste, deberías ver:\n";
echo "- Stock inicial: 4 unidades\n";
echo "- Stock final: 0 unidades (ERROR - debería ser 2)\n";
echo "\nSi funciona correctamente:\n";
echo "- Stock inicial: 4 unidades\n";
echo "- Stock final: 2 unidades (CORRECTO)\n";
?>
