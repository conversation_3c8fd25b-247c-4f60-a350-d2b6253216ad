<?php
session_start();
require_once 'auth_check.php'; // Esto verificará si el usuario está logueado

// Si llegamos aquí, el usuario está autenticado
header('Content-Type: application/json');

echo json_encode([
    'session_data' => $_SESSION,
    'logged_in' => isset($_SESSION['logged_in']) ? $_SESSION['logged_in'] : false,
    'user_id' => isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null,
    'username' => isset($_SESSION['username']) ? $_SESSION['username'] : null,
    'nombre' => isset($_SESSION['nombre']) ? $_SESSION['nombre'] : null,
    'rol' => isset($_SESSION['rol']) ? $_SESSION['rol'] : null
]);
?>