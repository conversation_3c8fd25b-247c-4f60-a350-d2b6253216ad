<?php
// Script de prueba para simular venta de 2 unidades
// Este script simula exactamente lo que debería hacer el sistema

require_once 'config/database.php';

function logTest($mensaje) {
    $timestamp = date('Y-m-d H:i:s');
    $microtime = microtime(true);
    $pid = getmypid();
    $logMessage = "[$timestamp] [PID:$pid] [TIME:$microtime] TEST: $mensaje";
    file_put_contents('test_simulacion.log', "$logMessage\n", FILE_APPEND | LOCK_EX);
    echo "$logMessage\n";
}

try {
    logTest("=== INICIANDO PRUEBA DE SIMULACIÓN DE 2 UNIDADES ===");
    
    // Conectar a la base de datos
    $conn = new PDO("mysql:host=localhost;dbname=tata_repuestos", "root", "N1c0l7as17");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    logTest("Conexión a base de datos establecida");
    
    // Verificar stock inicial
    $stmt = $conn->prepare("SELECT cantidad FROM stock WHERE repuesto_id = 56 AND almacen_id = 1");
    $stmt->execute();
    $stockInicial = $stmt->fetchColumn();
    logTest("Stock inicial: $stockInicial unidades");
    
    if ($stockInicial < 2) {
        logTest("ERROR: Stock insuficiente para la prueba. Se necesitan al menos 2 unidades.");
        exit(1);
    }
    
    // Simular la venta de 2 unidades
    $repuesto_id = 56;
    $almacen_id = 1;
    $cantidad_venta = 2;
    
    logTest("Simulando venta de $cantidad_venta unidades del repuesto $repuesto_id");
    
    // Iniciar transacción
    $conn->beginTransaction();
    logTest("Transacción iniciada");
    
    // 1. Registrar movimiento de inventario
    $stmtMovimiento = $conn->prepare("
        INSERT INTO movimiento_inventario (
            repuesto_id, almacen_id, tipo_movimiento, cantidad, 
            fecha_movimiento, referencia_documento, usuario, created_at
        ) VALUES (
            :repuesto_id, :almacen_id, 'SALIDA', :cantidad,
            NOW(), :referencia, :usuario, NOW()
        )
    ");
    
    $stmtMovimiento->bindParam(':repuesto_id', $repuesto_id);
    $stmtMovimiento->bindParam(':almacen_id', $almacen_id);
    $stmtMovimiento->bindParam(':cantidad', $cantidad_venta);
    $referencia = "TEST - Simulación de venta - " . date('d/m/Y, H:i:s');
    $stmtMovimiento->bindParam(':referencia', $referencia);
    $usuario = 'test_admin';
    $stmtMovimiento->bindParam(':usuario', $usuario);
    
    $stmtMovimiento->execute();
    logTest("Movimiento de inventario registrado: $cantidad_venta unidades");
    
    // 2. Actualizar stock
    $stmtStock = $conn->prepare("
        UPDATE stock 
        SET cantidad = cantidad - :cantidad 
        WHERE repuesto_id = :repuesto_id AND almacen_id = :almacen_id
    ");
    
    $stmtStock->bindParam(':cantidad', $cantidad_venta);
    $stmtStock->bindParam(':repuesto_id', $repuesto_id);
    $stmtStock->bindParam(':almacen_id', $almacen_id);
    
    $stmtStock->execute();
    $filasAfectadas = $stmtStock->rowCount();
    logTest("Stock actualizado. Filas afectadas: $filasAfectadas");
    
    // Verificar stock después de la actualización
    $stmt = $conn->prepare("SELECT cantidad FROM stock WHERE repuesto_id = 56 AND almacen_id = 1");
    $stmt->execute();
    $stockFinal = $stmt->fetchColumn();
    logTest("Stock final: $stockFinal unidades");
    
    // Calcular diferencia
    $diferencia = $stockInicial - $stockFinal;
    logTest("Diferencia calculada: $diferencia (debería ser $cantidad_venta)");
    
    if ($diferencia == $cantidad_venta) {
        logTest("✅ PRUEBA EXITOSA: El stock se redujo correctamente");
        $conn->commit();
        logTest("Transacción confirmada");
    } else {
        logTest("❌ PRUEBA FALLIDA: El stock no se redujo correctamente");
        $conn->rollBack();
        logTest("Transacción revertida");
    }
    
    logTest("=== PRUEBA COMPLETADA ===");
    
} catch (Exception $e) {
    logTest("ERROR: " . $e->getMessage());
    if (isset($conn)) {
        $conn->rollBack();
        logTest("Transacción revertida por error");
    }
}
?>
