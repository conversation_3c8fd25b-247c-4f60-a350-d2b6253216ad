const { test, expect } = require('@playwright/test');

test('Cerrar canvas DTE al hacer clic fuera de él', async ({ page }) => {
  // Navegar a la página principal
  await page.goto('http://localhost:8080/Tata%20Repuestos/');
  
  // Hacer clic en el botón para abrir el canvas DTE
  await page.click('#openDTEBtn');
  
  // Verificar que el canvas está visible
  const dteCanvas = page.locator('#dteCanvas');
  await expect(dteCanvas).toBeVisible();
  
  // Hacer clic en el overlay (fuera del canvas)
  await page.click('#dteOverlay');
  
  // Verificar que el canvas ya no está visible
  await expect(dteCanvas).not.toBeVisible();
});
