<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversor JSON a Tabla - Telqway</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
            max-width: 1200px;
            width: 100%;
            max-height: 90vh;
            overflow: auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1rem;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            flex-wrap: wrap;
            align-items: center;
        }

        .file-input-wrapper {
            position: relative;
            overflow: hidden;
            display: inline-block;
        }

        .file-input-wrapper input[type=file] {
            position: absolute;
            left: -9999px;
        }

        .file-input-label {
            background: #3498db;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
            font-weight: 500;
        }

        .file-input-label:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #2ecc71;
            color: white;
        }

        .btn-primary:hover {
            background: #27ae60;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #e74c3c;
            color: white;
        }

        .btn-secondary:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid #ecf0f1;
            margin-bottom: 20px;
        }

        .tab {
            padding: 12px 24px;
            background: #ecf0f1;
            border: none;
            cursor: pointer;
            border-radius: 8px 8px 0 0;
            margin-right: 5px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .tab.active {
            background: #3498db;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .table-container {
            overflow-x: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            max-height: 400px;
            overflow-y: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        th {
            background: #34495e;
            color: white;
            padding: 12px 8px;
            text-align: left;
            position: sticky;
            top: 0;
            z-index: 10;
            font-weight: 600;
        }

        td {
            padding: 10px 8px;
            border-bottom: 1px solid #eee;
        }

        tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        tr:hover {
            background-color: #e3f2fd;
        }

        .number {
            text-align: right;
            font-family: 'Courier New', monospace;
        }

        .status {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Conversor JSON a Tabla</h1>
            <p>Convierte archivos JSON de Telqway a formato tabla para Excel/CSV</p>
        </div>

        <div class="controls">
            <div class="file-input-wrapper">
                <input type="file" id="jsonFile" accept=".json" />
                <label for="jsonFile" class="file-input-label">📁 Seleccionar JSON</label>
            </div>
            <button onclick="processFile()" class="btn btn-primary">🔄 Procesar</button>
            <button onclick="exportToCSV()" class="btn btn-secondary">📊 Exportar CSV</button>
            <button onclick="exportToExcel()" class="btn btn-secondary">📈 Exportar Excel</button>
        </div>

        <div id="status" class="status" style="display: none;"></div>

        <div id="results" style="display: none;">
            <div class="stats" id="statsContainer"></div>

            <div class="tabs">
                <button class="tab active" onclick="showTab('caratula')">📋 Carátula</button>
                <button class="tab" onclick="showTab('resumenes')">📊 Resúmenes</button>
                <button class="tab" onclick="showTab('detalle')">📝 Detalle Ventas</button>
            </div>

            <div id="caratula" class="tab-content active">
                <div class="table-container">
                    <table id="caratulaTable"></table>
                </div>
            </div>

            <div id="resumenes" class="tab-content">
                <div class="table-container">
                    <table id="resumenesTable"></table>
                </div>
            </div>

            <div id="detalle" class="tab-content">
                <div class="table-container">
                    <table id="detalleTable"></table>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentData = null;

        // Cargar el archivo JSON automáticamente
        window.addEventListener('load', async function() {
            try {
                const response = await window.fs.readFile('C:/Users/<USER>/Downloads/response_tata_junio_ventas', { encoding: 'utf8' });
                currentData = JSON.parse(response);
                processData();
                showStatus('Archivo cargado automáticamente', 'success');
            } catch (error) {
                console.log('No se pudo cargar el archivo automáticamente, esperando selección manual');
            }
        });

        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    status.style.display = 'none';
                }, 3000);
            }
        }

        function processFile() {
            const fileInput = document.getElementById('jsonFile');
            const file = fileInput.files[0];

            if (!file) {
                showStatus('Por favor selecciona un archivo JSON', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    currentData = JSON.parse(e.target.result);
                    processData();
                    showStatus('Archivo procesado exitosamente', 'success');
                } catch (error) {
                    showStatus('Error al leer el archivo JSON: ' + error.message, 'error');
                }
            };
            reader.readAsText(file);
        }

        function processData() {
            if (!currentData) return;

            // Mostrar estadísticas
            showStats();
            
            // Procesar cada sección
            createCaratulaTable();
            createResumenesTable();
            createDetalleTable();

            document.getElementById('results').style.display = 'block';
        }

        function showStats() {
            const stats = document.getElementById('statsContainer');
            const ventasData = currentData.ventas || {};
            
            let totalFacturas = 0;
            let totalMonto = 0;
            
            if (ventasData.resumenes) {
                ventasData.resumenes.forEach(resumen => {
                    totalFacturas += resumen.totalDocumentos || 0;
                    totalMonto += resumen.montoTotal || 0;
                });
            }

            stats.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${totalFacturas}</div>
                    <div class="stat-label">Total Documentos</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">$${totalMonto.toLocaleString()}</div>
                    <div class="stat-label">Monto Total</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${ventasData.detalleVentas ? ventasData.detalleVentas.length : 0}</div>
                    <div class="stat-label">Registros Detalle</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${currentData.caratula?.nombreMes || 'N/A'} ${currentData.caratula?.anio || ''}</div>
                    <div class="stat-label">Período</div>
                </div>
            `;
        }

        function createCaratulaTable() {
            const table = document.getElementById('caratulaTable');
            const caratula = currentData.caratula || {};
            
            let html = '<thead><tr><th>Campo</th><th>Valor</th></tr></thead><tbody>';
            
            Object.entries(caratula).forEach(([key, value]) => {
                html += `<tr><td><strong>${formatFieldName(key)}</strong></td><td>${value || 'N/A'}</td></tr>`;
            });
            
            html += '</tbody>';
            table.innerHTML = html;
        }

        function createResumenesTable() {
            const table = document.getElementById('resumenesTable');
            const resumenes = currentData.ventas?.resumenes || [];
            
            if (resumenes.length === 0) {
                table.innerHTML = '<thead><tr><th>Sin datos</th></tr></thead>';
                return;
            }

            let html = `
                <thead>
                    <tr>
                        <th>Tipo DTE</th>
                        <th>Descripción</th>
                        <th>Total Docs</th>
                        <th>Monto Exento</th>
                        <th>Monto Neto</th>
                        <th>IVA Recuperable</th>
                        <th>Monto Total</th>
                        <th>Estado</th>
                    </tr>
                </thead>
                <tbody>
            `;

            resumenes.forEach(resumen => {
                html += `
                    <tr>
                        <td class="number">${resumen.tipoDte}</td>
                        <td>${resumen.tipoDteString}</td>
                        <td class="number">${resumen.totalDocumentos}</td>
                        <td class="number">$${(resumen.montoExento || 0).toLocaleString()}</td>
                        <td class="number">$${(resumen.montoNeto || 0).toLocaleString()}</td>
                        <td class="number">$${(resumen.ivaRecuperable || 0).toLocaleString()}</td>
                        <td class="number">$${(resumen.montoTotal || 0).toLocaleString()}</td>
                        <td>${resumen.estado || 'N/A'}</td>
                    </tr>
                `;
            });

            html += '</tbody>';
            table.innerHTML = html;
        }

        function createDetalleTable() {
            const table = document.getElementById('detalleTable');
            const detalle = currentData.ventas?.detalleVentas || [];
            
            if (detalle.length === 0) {
                table.innerHTML = '<thead><tr><th>Sin datos de detalle</th></tr></thead>';
                return;
            }

            let html = `
                <thead>
                    <tr>
                        <th>Tipo</th>
                        <th>Folio</th>
                        <th>Fecha Emisión</th>
                        <th>RUT Cliente</th>
                        <th>Razón Social</th>
                        <th>Monto Neto</th>
                        <th>IVA</th>
                        <th>Total</th>
                        <th>Estado</th>
                    </tr>
                </thead>
                <tbody>
            `;

            detalle.forEach(item => {
                const fechaEmision = new Date(item.fechaEmision).toLocaleDateString('es-CL');
                html += `
                    <tr>
                        <td>${item.tipoDte}</td>
                        <td class="number">${item.folio}</td>
                        <td>${fechaEmision}</td>
                        <td>${item.rutCliente}</td>
                        <td>${item.razonSocial}</td>
                        <td class="number">$${(item.montoNeto || 0).toLocaleString()}</td>
                        <td class="number">$${(item.montoIva || 0).toLocaleString()}</td>
                        <td class="number">$${(item.montoTotal || 0).toLocaleString()}</td>
                        <td>${item.estado}</td>
                    </tr>
                `;
            });

            html += '</tbody>';
            table.innerHTML = html;
        }

        function formatFieldName(fieldName) {
            const translations = {
                rutEmpresa: 'RUT Empresa',
                nombreMes: 'Nombre Mes',
                mes: 'Mes',
                anio: 'Año',
                dia: 'Día',
                periodo: 'Período'
            };
            return translations[fieldName] || fieldName;
        }

        function showTab(tabName) {
            // Ocultar todos los contenidos
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Desactivar todos los tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Mostrar el contenido seleccionado
            document.getElementById(tabName).classList.add('active');
            
            // Activar el tab seleccionado
            event.target.classList.add('active');
        }

        function exportToCSV() {
            if (!currentData) {
                showStatus('No hay datos para exportar', 'error');
                return;
            }

            let csvContent = '';
            
            // Exportar resúmenes
            csvContent += "RESÚMENES DE VENTAS\n";
            csvContent += "Tipo DTE,Descripción,Total Documentos,Monto Exento,Monto Neto,IVA Recuperable,Monto Total,Estado\n";
            
            const resumenes = currentData.ventas?.resumenes || [];
            resumenes.forEach(resumen => {
                csvContent += `${resumen.tipoDte},"${resumen.tipoDteString}",${resumen.totalDocumentos},${resumen.montoExento},${resumen.montoNeto},${resumen.ivaRecuperable},${resumen.montoTotal},"${resumen.estado || ''}"\n`;
            });
            
            csvContent += "\n\nDETALLE DE VENTAS\n";
            csvContent += "Tipo DTE,Folio,Fecha Emisión,RUT Cliente,Razón Social,Monto Exento,Monto Neto,Monto IVA,Monto Total,Estado\n";
            
            const detalle = currentData.ventas?.detalleVentas || [];
            detalle.forEach(item => {
                const fechaEmision = new Date(item.fechaEmision).toLocaleDateString('es-CL');
                csvContent += `${item.tipoDte},${item.folio},"${fechaEmision}","${item.rutCliente}","${item.razonSocial}",${item.montoExento},${item.montoNeto},${item.montoIva},${item.montoTotal},"${item.estado}"\n`;
            });

            // Descargar archivo
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `ventas_telqway_${currentData.caratula?.periodo || 'export'}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            showStatus('Archivo CSV exportado exitosamente', 'success');
        }

        function exportToExcel() {
            showStatus('Funcionalidad de Excel disponible con librerías adicionales', 'error');
        }
    </script>
</body>
</html>