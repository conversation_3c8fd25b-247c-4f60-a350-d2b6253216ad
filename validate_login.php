<?php
session_start();
require_once 'db_connection.php';

// Función para registrar intentos de inicio de sesión
function logLoginAttempt($username, $success, $ip, $conn) {
    try {
        $sql = "INSERT INTO tb_login_logs (username, ip_address, success, fecha) 
                VALUES (?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$username, $ip, $success ? 1 : 0]);
    } catch (Exception $e) {
        // Si la tabla no existe, la creamos
        if (strpos($e->getMessage(), "doesn't exist") !== false) {
            $createLogTable = "CREATE TABLE tb_login_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL,
                ip_address VARCHAR(45) NOT NULL,
                success BOOLEAN NOT NULL,
                fecha DATETIME NOT NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($createLogTable);
            
            // Intentar nuevamente la inserción
            $stmt = $conn->prepare($sql);
            $stmt->execute([$username, $ip, $success ? 1 : 0]);
        }
    }
}

// Función para verificar si el usuario está bloqueado por demasiados intentos fallidos
function isUserLocked($username, $conn) {
    $sql = "SELECT intentos_fallidos FROM tb_usuarios WHERE username = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$username]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user && $user['intentos_fallidos'] >= 5) {
        return true;
    }
    return false;
}

// Función para incrementar el contador de intentos fallidos
function incrementFailedAttempts($username, $conn) {
    $sql = "UPDATE tb_usuarios SET intentos_fallidos = intentos_fallidos + 1 WHERE username = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$username]);
}

// Función para resetear el contador de intentos fallidos
function resetFailedAttempts($username, $conn) {
    $sql = "UPDATE tb_usuarios SET intentos_fallidos = 0, ultimo_login = NOW() WHERE username = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$username]);
}

// Inicializar variables
$error = '';
$username = '';

// Verificar si se envió el formulario
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Obtener y sanitizar datos del formulario
    $username = filter_input(INPUT_POST, 'username', FILTER_SANITIZE_STRING);
    $password = $_POST['password']; // No sanitizamos la contraseña para no alterarla
    
    // Validar que los campos no estén vacíos
    if (empty($username) || empty($password)) {
        $error = 'Por favor, complete todos los campos.';
    } else {
        try {
            $conn = getConnection();
            
            // Verificar si el usuario está bloqueado
            if (isUserLocked($username, $conn)) {
                $error = 'Su cuenta ha sido bloqueada por demasiados intentos fallidos. Por favor, contacte al administrador.';
                logLoginAttempt($username, false, $_SERVER['REMOTE_ADDR'], $conn);
            } else {
                // Buscar el usuario en la base de datos
                $sql = "SELECT id, username, password, nombre, rol, activo FROM tb_usuarios WHERE username = ?";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$username]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                // Verificar si el usuario existe y está activo
                if ($user && $user['activo']) {
                    // Verificar la contraseña
                    if (password_verify($password, $user['password'])) {
                        // Contraseña correcta, iniciar sesión
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['username'] = $user['username'];
                        $_SESSION['nombre'] = $user['nombre'];
                        $_SESSION['rol'] = $user['rol'];
                        $_SESSION['logged_in'] = true;
                        $_SESSION['login_time'] = time();
                        
                        // Generar un token de sesión para prevenir CSRF
                        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
                        
                        // Resetear contador de intentos fallidos
                        resetFailedAttempts($username, $conn);
                        
                        // Registrar inicio de sesión exitoso
                        logLoginAttempt($username, true, $_SERVER['REMOTE_ADDR'], $conn);
                        
                        // Redirigir a la página principal
                        header('Location: index.php');
                        exit;
                    } else {
                        // Contraseña incorrecta
                        $error = 'Usuario o contraseña incorrectos.';
                        incrementFailedAttempts($username, $conn);
                        logLoginAttempt($username, false, $_SERVER['REMOTE_ADDR'], $conn);
                    }
                } else if ($user && !$user['activo']) {
                    // Usuario existe pero está desactivado
                    $error = 'Su cuenta está desactivada. Por favor, contacte al administrador.';
                    logLoginAttempt($username, false, $_SERVER['REMOTE_ADDR'], $conn);
                } else {
                    // Usuario no existe
                    $error = 'Usuario o contraseña incorrectos.';
                    logLoginAttempt($username, false, $_SERVER['REMOTE_ADDR'], $conn);
                }
            }
        } catch (Exception $e) {
            $error = 'Error en el servidor. Por favor, intente nuevamente más tarde.';
            error_log('Error en validate_login.php: ' . $e->getMessage());
        }
    }
}

// Si hay un error, redirigir a login.php con el mensaje de error
if (!empty($error)) {
    $_SESSION['login_error'] = $error;
    $_SESSION['username_prefill'] = $username; // Para rellenar el campo de usuario
    header('Location: login.php');
    exit;
}
?>
