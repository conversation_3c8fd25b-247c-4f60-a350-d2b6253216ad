<?php
require_once 'db_connection.php';
header('Content-Type: application/json');

try {
    $conn = getConnection();
    
    // Obtener el ID del producto desde la URL
    $repuesto_id = isset($_GET['id']) ? intval($_GET['id']) : 56; // Por defecto el producto de prueba
    
    // Consulta para obtener el stock actual
    $stmt = $conn->prepare("
        SELECT 
            r.id,
            r.sku,
            r.nombre,
            s.cantidad,
            s.lote,
            s.almacen_id,
            s.created_at,
            s.updated_at
        FROM repuesto r
        LEFT JOIN stock s ON r.id = s.repuesto_id
        WHERE r.id = :repuesto_id
        ORDER BY s.created_at ASC
    ");
    
    $stmt->bindParam(':repuesto_id', $repuesto_id);
    $stmt->execute();
    $stock_detalle = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Consulta para obtener el stock total
    $stmt_total = $conn->prepare("
        SELECT 
            r.id,
            r.sku,
            r.nombre,
            SUM(COALESCE(s.cantidad, 0)) as stock_total
        FROM repuesto r
        LEFT JOIN stock s ON r.id = s.repuesto_id AND s.almacen_id = 1
        WHERE r.id = :repuesto_id
        GROUP BY r.id, r.sku, r.nombre
    ");
    
    $stmt_total->bindParam(':repuesto_id', $repuesto_id);
    $stmt_total->execute();
    $stock_total = $stmt_total->fetch(PDO::FETCH_ASSOC);
    
    // Consulta para obtener los últimos movimientos
    $stmt_movimientos = $conn->prepare("
        SELECT 
            tipo_movimiento,
            cantidad,
            fecha_movimiento,
            referencia_documento,
            usuario
        FROM movimiento_inventario
        WHERE repuesto_id = :repuesto_id
        ORDER BY fecha_movimiento DESC
        LIMIT 10
    ");
    
    $stmt_movimientos->bindParam(':repuesto_id', $repuesto_id);
    $stmt_movimientos->execute();
    $movimientos = $stmt_movimientos->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'status' => 'success',
        'repuesto_id' => $repuesto_id,
        'stock_detalle' => $stock_detalle,
        'stock_total' => $stock_total,
        'ultimos_movimientos' => $movimientos,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>
